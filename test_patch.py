#!/usr/bin/env python
import requests
import json

# Connexion
login_data = {'email': '<EMAIL>', 'password': 'testpass123'}
response = requests.post('http://localhost:8000/api/login/', json=login_data)

if response.status_code == 200:
    token = response.json()['access_token']
    print(f'Token obtenu: {token[:50]}...')

    # Test de mise à jour du profil
    headers = {'Authorization': f'Bearer {token}'}
    update_data = {
        'captain_profile': {
            'experience': '5 years navigating coastal waters',
            'average_rating': '4.75',
            'total_trips': 120,
            'wallet_balance': '350.00',
            'is_available': True,
            'current_location': 'Porto-Novo, Bénin',
            'license_number': 'BJ-CPT-2024-001',
            'license_expiry_date': '2026-12-31',
            'years_of_experience': 5,
            'certifications': ['First Aid Certified', 'Advanced Navigation'],
            'specializations': ['Fishing', 'Tourism', 'Cargo'],
            'availability_status': 'AVAILABLE',
            'boat_photos': [
                'https://example.com/images/boat1.jpg',
                'https://example.com/images/boat2.jpg'
            ],
            'rate_per_km': '1.50',
            'rate_per_hour': '20.00'
        }
    }

    patch_response = requests.patch('http://localhost:8000/api/profile/', json=update_data, headers=headers)
    print(f'Status: {patch_response.status_code}')

    if patch_response.status_code == 200:
        result = patch_response.json()
        captain_profile = result.get('captain_profile', {})
        print(f'Experience: {captain_profile.get("experience", "Not updated")}')
        print(f'License: {captain_profile.get("license_number", "Not updated")}')
        print(f'Rate per hour: {captain_profile.get("rate_per_hour", "Not updated")}')
        print(f'Total trips: {captain_profile.get("total_trips", "Not updated")}')
    else:
        print(f'Error: {patch_response.text}')
else:
    print(f'Login failed: {response.text}')
