"""
Vues pour le support hors ligne du chatbot RAG.

Ce module définit les vues API pour récupérer les données nécessaires au support hors ligne,
notamment les FAQ et les documents les plus importants.
"""

import logging
from django.http import JsonResponse
from django.views.decorators.cache import cache_page
from django.utils.decorators import method_decorator
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from .rag_service import RagService

# Configurer le logger
logger = logging.getLogger(__name__)

# Initialiser le service RAG
rag_service = RagService()

class OfflineDataAPIView(APIView):
    """
    API pour récupérer les données nécessaires au support hors ligne.

    Cette vue permet de récupérer un package de données pour le support hors ligne,
    notamment les FAQ et les documents les plus importants.
    """

    permission_classes = [AllowAny]  # Pour les tests, à remplacer par IsAuthenticated en production

    @method_decorator(cache_page(60 * 60 * 24))  # Cache pendant 24 heures
    def get(self, request, format=None):
        """
        Récupère les données pour le support hors ligne.

        Args:
            request: La requête HTTP.
            format: Le format de la réponse.

        Returns:
            Response: Les données pour le support hors ligne.
        """
        try:
            # Récupérer le profil de l'utilisateur
            profile = request.query_params.get('profile', None)

            # Exporter les données pour le support hors ligne
            offline_data = rag_service.export_offline_data()

            # Si un profil est spécifié, filtrer les données
            if profile:
                filtered_data = {
                    'faqs': {
                        'client': offline_data['faqs']['client'] if profile == 'Client' else [],
                        'captain': offline_data['faqs']['captain'] if profile == 'Capitaine' else [],
                        'establishment': offline_data['faqs']['establishment'] if profile == 'Établissement' else [],
                        'general': offline_data['faqs']['general']
                    },
                    'documents': offline_data['documents'],
                    'version': offline_data['version'],
                    'generated_at': offline_data['generated_at'],
                    'expires_at': offline_data['expires_at']
                }
                return Response(filtered_data)

            return Response(offline_data)

        except Exception as e:
            logger.error(f"Erreur lors de l'exportation des données hors ligne: {str(e)}")
            return Response(
                {'error': 'Une erreur est survenue lors de l\'exportation des données hors ligne.'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class OfflineFAQsAPIView(APIView):
    """
    API pour récupérer les FAQ pour le support hors ligne.

    Cette vue permet de récupérer uniquement les FAQ pour le support hors ligne,
    filtrées par profil si spécifié.
    """

    permission_classes = [AllowAny]  # Pour les tests, à remplacer par IsAuthenticated en production

    @method_decorator(cache_page(60 * 60 * 12))  # Cache pendant 12 heures
    def get(self, request, format=None):
        """
        Récupère les FAQ pour le support hors ligne.

        Args:
            request: La requête HTTP.
            format: Le format de la réponse.

        Returns:
            Response: Les FAQ pour le support hors ligne.
        """
        try:
            # Récupérer le profil de l'utilisateur et la limite
            profile = request.query_params.get('profile', None)
            limit = int(request.query_params.get('limit', 50))

            # Récupérer les FAQ
            faqs = rag_service.get_offline_faqs(profile=profile, limit=limit)

            return Response({'faqs': faqs})

        except Exception as e:
            logger.error(f"Erreur lors de la récupération des FAQ hors ligne: {str(e)}")
            return Response(
                {'error': 'Une erreur est survenue lors de la récupération des FAQ hors ligne.'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
