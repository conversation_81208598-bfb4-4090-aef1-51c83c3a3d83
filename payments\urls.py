"""
Module de configuration des URLs pour l'application payments.

Ce module définit les routes URL pour l'API REST de l'application payments,
permettant d'accéder aux différentes vues.
"""

from django.urls import path
from .views_api import (
    WalletDetailView, WalletRechargeView,
    TipPaymentView, CarbonOffsetView, RefundPaymentView,
    StripeWebhookView
)
from .views_extended import (
    ShuttlePaymentView,
    MaintenancePaymentView, PromotionPaymentView,
    TransactionDetailView, TransactionListView
)
from .views_trip_adapter import TripPaymentView, SharedTripPaymentView
from .webhooks import stripe_webhook
from .views_admin import AdminWalletAddFundsView

urlpatterns = [
    # URLs pour les portefeuilles
    path('wallet/', WalletDetailView.as_view(), name='wallet-detail'),
    path('wallet/recharge/', WalletRechargeView.as_view(), name='wallet-recharge'),
    path('wallet/add_credits/', WalletRechargeView.as_view(), name='wallet-add-credits'),
    
    # URL admin pour recharger un portefeuille
    path('admin/wallet/add-funds/', AdminWalletAddFundsView.as_view(), name='admin-wallet-add-funds'),

    # URLs pour les paiements de courses (utilisant uniquement la terminologie 'trip')
    path('trip/', TripPaymentView.as_view(), name='trip-payment'),
    path('trips/<int:trip_id>/pay/', TripPaymentView.as_view(), name='trip-payment'),
    path('trips/<int:trip_id>/shared_pay/', SharedTripPaymentView.as_view(), name='shared-trip-payment'),
    path('tip/', TipPaymentView.as_view(), name='tip-payment'),
    
    # URLs pour les paiements de navettes
    path('shuttles/<int:shuttle_id>/pay/', ShuttlePaymentView.as_view(), name='shuttle-payment'),
    
    # URLs pour les paiements de maintenance
    path('maintenance/<int:maintenance_id>/pay/', MaintenancePaymentView.as_view(), name='maintenance-payment'),
    
    # URLs pour les paiements de promotions
    path('promotions/', PromotionPaymentView.as_view(), name='promotion-payment'),
    
    # URLs pour les transactions
    path('transactions/', TransactionListView.as_view(), name='transaction-list'),
    path('transactions/<str:id>/', TransactionDetailView.as_view(), name='transaction-detail'),
    path('transactions/<str:id>/refund/', RefundPaymentView.as_view(), name='transaction-refund'),
    
    # URLs pour les offsets carbone
    path('carbon-offset/', CarbonOffsetView.as_view(), name='carbon-offset'),
    
    # URLs pour les webhooks
    path('webhook/', stripe_webhook, name='stripe-webhook'),
    path('webhooks/stripe/', stripe_webhook, name='stripe-webhook-v2'),
]
