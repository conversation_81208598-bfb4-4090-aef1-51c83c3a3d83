from rest_framework import serializers
from .models import Trip, Shuttle
from accounts.serializers import ClientSerializer, CaptainSerializer, EstablishmentSerializer
from boats.serializers import BoatSerializer

class TripSerializer(serializers.ModelSerializer):
    client = ClientSerializer(read_only=True)
    captain = CaptainSerializer(read_only=True)
    boat = BoatSerializer(read_only=True)
    establishment = EstablishmentSerializer(read_only=True)

    class Meta:
        model = Trip
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'status', 'actual_start_time',
                          'actual_end_time', 'current_location', 'tracking_data',
                          'total_price', 'payment_status')

    def validate(self, data):
        # Vérifier que l'heure de fin est après l'heure de début
        if data.get('scheduled_end_time') and data.get('scheduled_start_time'):
            if data['scheduled_end_time'] <= data['scheduled_start_time']:
                raise serializers.ValidationError({
                    "scheduled_end_time": "L'heure de fin doit être après l'heure de début"
                })

        # Vérifier que le nombre de passagers ne dépasse pas la capacité du bateau
        if data.get('passenger_count') and data.get('boat'):
            if data['passenger_count'] > data['boat'].capacity:
                raise serializers.ValidationError({
                    "passenger_count": "Le nombre de passagers dépasse la capacité du bateau"
                })

        return data

class TripListSerializer(serializers.ModelSerializer):
    """Sérialiseur simplifié pour la liste des courses"""
    client_name = serializers.CharField(source='client.user.get_full_name', read_only=True)
    captain_name = serializers.CharField(source='captain.user.get_full_name', read_only=True)
    boat_name = serializers.CharField(source='boat.name', read_only=True)

    class Meta:
        model = Trip
        fields = ('id', 'client_name', 'captain_name', 'boat_name', 'start_location',
                 'end_location', 'scheduled_start_time', 'status', 'total_price')

class ShuttleSerializer(serializers.ModelSerializer):
    establishment = EstablishmentSerializer(read_only=True)
    boat = BoatSerializer(read_only=True)
    captain = CaptainSerializer(read_only=True)

    class Meta:
        model = Shuttle
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'current_bookings')

    def validate(self, data):
        # Vérifier que l'heure d'arrivée est après l'heure de départ
        if data.get('arrival_time') and data.get('departure_time'):
            if data['arrival_time'] <= data['departure_time']:
                raise serializers.ValidationError({
                    "arrival_time": "L'heure d'arrivée doit être après l'heure de départ"
                })

        # Vérifier que la capacité maximale est cohérente avec le bateau
        if data.get('max_capacity') and data.get('boat'):
            if data['max_capacity'] > data['boat'].capacity:
                raise serializers.ValidationError({
                    "max_capacity": "La capacité maximale ne peut pas dépasser la capacité du bateau"
                })

        return data

class ShuttleListSerializer(serializers.ModelSerializer):
    """Sérialiseur simplifié pour la liste des navettes"""
    establishment_name = serializers.CharField(source='establishment.business_name', read_only=True)
    boat_name = serializers.CharField(source='boat.name', read_only=True)
    available_seats = serializers.SerializerMethodField()

    class Meta:
        model = Shuttle
        fields = ('id', 'route_name', 'establishment_name', 'boat_name',
                 'departure_time', 'arrival_time', 'price_per_person',
                 'available_seats', 'status')

    def get_available_seats(self, obj):
        return max(0, obj.max_capacity - obj.current_bookings)
