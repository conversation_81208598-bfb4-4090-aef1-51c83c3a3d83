from django.urls import path
from . import views

app_name = 'trips'

urlpatterns = [
    # URLs existantes
    path('', views.TripListCreateView.as_view(), name='trip-list-create'),
    path('<int:pk>/', views.TripDetailView.as_view(), name='trip-detail'),
    path('shuttles/', views.ShuttleListCreateView.as_view(), name='shuttle-list-create'),
    path('shuttles/<int:pk>/', views.ShuttleDetailView.as_view(), name='shuttle-detail'),

    # Nouveaux endpoints pour les trois types de courses
    path('requests/simple/', views.SimpleTripRequestView.as_view(), name='simple-trip-request'),
    path('requests/hourly/', views.HourlyTripRequestView.as_view(), name='hourly-trip-request'),
    path('requests/shuttle/', views.ShuttleTripRequestView.as_view(), name='shuttle-trip-request'),
    path('requests/<int:pk>/', views.TripRequestDetailView.as_view(), name='trip-request-detail'),

    # Gestion des devis
    path('quotes/<int:quote_id>/accept/', views.TripQuoteAcceptView.as_view(), name='quote-accept'),

    # Nettoyage automatique
    path('cleanup/expired/', views.ExpiredTripRequestCleanupView.as_view(), name='cleanup-expired'),
]
