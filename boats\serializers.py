from rest_framework import serializers
from .models import Boat, MaintenanceRecord

class MaintenanceRecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = MaintenanceRecord
        fields = '__all__'
        read_only_fields = ('boat',)

class BoatSerializer(serializers.ModelSerializer):
    maintenance_records = MaintenanceRecordSerializer(many=True, read_only=True)
    captain = serializers.SerializerMethodField()
    establishment = serializers.SerializerMethodField()

    def validate_photos(self, value):
        if not isinstance(value, list):
            raise serializers.ValidationError('Le champ photos doit être une liste de liens.')
        if len(value) > 4:
            raise serializers.ValidationError('Vous pouvez fournir jusqu’à 4 photos maximum.')
        for url in value:
            if not isinstance(url, str) or not url.startswith('http'):
                raise serializers.ValidationError('Chaque photo doit être un lien valide.')
        return value

    def validate_radius(self, value):
        if not (1 <= value <= 100):
            raise serializers.ValidationError('Le rayon doit être compris entre 1 et 100 km.')
        return value

    def validate_capacity(self, value):
        if value is not None and value <= 0:
            raise serializers.ValidationError("La capacité doit être supérieure à 0")
        return value

    def get_captain(self, obj):
        """Retourne les informations du capitaine sans récursion"""
        captain = getattr(obj, 'captain', None)
        if captain:
            return {
                'id': captain.pk,  # Utiliser pk au lieu de id car Captain utilise user comme primary_key
                'user': {
                    'id': captain.user.id,
                    'email': captain.user.email,
                    'first_name': captain.user.first_name,
                    'last_name': captain.user.last_name,
                    'phone_number': captain.user.phone_number,
                    'profile_picture': captain.user.profile_picture.url if captain.user.profile_picture and hasattr(captain.user.profile_picture, 'url') else '',
                },
                'experience': captain.experience,
                'average_rating': captain.average_rating,
                'total_trips': captain.total_trips,
                'is_available': captain.is_available,
                'license_number': captain.license_number,
                'years_of_experience': captain.years_of_experience,
                'rate_per_hour': captain.rate_per_hour,
            }
        return None

    def get_establishment(self, obj):
        """Retourne les informations de l'établissement sans récursion"""
        establishment = getattr(obj, 'establishment', None)
        if establishment:
            return {
                'id': establishment.id,
                'user': {
                    'id': establishment.user.id,
                    'email': establishment.user.email,
                    'first_name': establishment.user.first_name,
                    'last_name': establishment.user.last_name,
                    'phone_number': establishment.user.phone_number,
                },
                'name': establishment.name,
                'type': establishment.type,
                'address': establishment.address,
                'description': establishment.description,
                'main_photo': establishment.main_photo,
                'average_rating': establishment.average_rating,
                'website': establishment.website,
            }
        return None

    class Meta:
        model = Boat
        fields = (
            'id', 'name', 'registration_number', 'boat_type', 'capacity', 'color',
            'fuel_type', 'fuel_consumption', 'photos', 'zone_served', 'radius',
            'captain', 'establishment', 'is_available', 'last_maintenance', 'next_maintenance',
            'created_at', 'updated_at', 'maintenance_records'
        )
        read_only_fields = ('created_at', 'updated_at', 'maintenance_records')
        extra_kwargs = {
            'zone_served': {'required': False, 'allow_blank': True},
            'radius': {'required': False},
            'photos': {'required': False},
        }


    def validate_capacity(self, value):
        if value <= 0:
            raise serializers.ValidationError("La capacité doit être supérieure à 0")
        return value

    def validate_year(self, value):
        from datetime import datetime
        current_year = datetime.now().year
        if value > current_year:
            raise serializers.ValidationError("L'année ne peut pas être dans le futur")
        if value < 1900:
            raise serializers.ValidationError("L'année ne peut pas être antérieure à 1900")
        return value

class BoatListSerializer(serializers.ModelSerializer):
    """Sérialiseur simplifié pour la liste des bateaux"""
    captain_name = serializers.CharField(source='captain.user.get_full_name', read_only=True)
    establishment_name = serializers.CharField(source='establishment.business_name', read_only=True)

    class Meta:
        model = Boat
        fields = ('id', 'name', 'registration_number', 'boat_type', 'capacity',
                 'status', 'captain_name', 'establishment_name', 'location')

class BoatDetailSerializer(BoatSerializer):
    """Sérialiseur détaillé pour un bateau spécifique"""
    next_maintenance = serializers.SerializerMethodField()
    last_maintenance = MaintenanceRecordSerializer(read_only=True)

    class Meta(BoatSerializer.Meta):
        fields = BoatSerializer.Meta.fields + ('next_maintenance', 'last_maintenance')

    def get_next_maintenance(self, obj):
        next_maintenance = obj.maintenance_records.filter(
            scheduled_date__gt=serializers.DateTimeField().to_representation(
                serializers.timezone.now()
            )
        ).first()
        if next_maintenance:
            return MaintenanceRecordSerializer(next_maintenance).data
        return None
