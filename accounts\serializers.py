from rest_framework import serializers
from django.contrib.auth.password_validation import validate_password
from .models import User, <PERSON><PERSON>, Captain, Establishment

class UserSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, required=True, validators=[validate_password])
    password2 = serializers.CharField(write_only=True, required=True)
    profile_picture = serializers.URLField(allow_blank=True, required=False)

    class Meta:
        model = User
        fields = ('id', 'email', 'password', 'password2', 'first_name', 'last_name',
                 'phone_number', 'type', 'profile_picture', 'is_active')
        extra_kwargs = {
            'email': {'required': True},
            'first_name': {'required': True},
            'last_name': {'required': True},
        }

    def validate(self, attrs):
        if attrs['password'] != attrs['password2']:
            raise serializers.ValidationError({"password": "Les mots de passe ne correspondent pas"})
        return attrs

    def create(self, validated_data):
        validated_data.pop('password2')
        # Permettre l’enregistrement direct d’une URL pour profile_picture
        profile_picture = validated_data.get('profile_picture')
        if profile_picture and isinstance(profile_picture, str):
            validated_data['profile_picture'] = profile_picture
        user = User.objects.create_user(**validated_data)
        return user

    def update(self, instance, validated_data):
        # Permettre l’enregistrement direct d’une URL pour profile_picture
        profile_picture = validated_data.get('profile_picture')
        if profile_picture and isinstance(profile_picture, str):
            instance.profile_picture = profile_picture
            validated_data.pop('profile_picture', None)
        return super().update(instance, validated_data)

# Construction correcte de extra_kwargs pour ClientSerializer
from django.db import models as _models

_client_fields = ('user', 'wallet_balance', 'date_of_birth', 'nationality', 'preferred_language',
                  'emergency_contact_name', 'emergency_contact_phone')
_client_extra_kwargs = {}
for field in _client_fields:
    if field != 'user':
        _client_extra_kwargs[field] = {'required': False, 'allow_null': True}
        # N’ajoute allow_blank que si c’est un champ texte
        model_field = Client._meta.get_field(field)
        if isinstance(model_field, (_models.CharField, _models.TextField)):
            _client_extra_kwargs[field]['allow_blank'] = True

class ClientSerializer(serializers.ModelSerializer):
    user = UserSerializer(required=False)

    class Meta:
        model = Client
        fields = _client_fields
        extra_kwargs = _client_extra_kwargs

    def update(self, instance, validated_data):
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance

# Construction correcte de extra_kwargs pour EstablishmentSerializer
_est_fields = ('user', 'name', 'type', 'address', 'description', 'main_photo',
                  'secondary_photos', 'wallet_balance', 'business_name', 'business_type',
                  'registration_number', 'tax_id', 'opening_hours', 'services_offered',
                  'average_rating', 'location_coordinates', 'website', 'social_media')
_est_extra_kwargs = {}
for field in _est_fields:
    if field != 'user':
        _est_extra_kwargs[field] = {'required': False, 'allow_null': True}
        model_field = Establishment._meta.get_field(field)
        if isinstance(model_field, (_models.CharField, _models.TextField)):
            _est_extra_kwargs[field]['allow_blank'] = True

class EstablishmentSerializer(serializers.ModelSerializer):
    user = UserSerializer(required=False)

    class Meta:
        model = Establishment
        fields = _est_fields
        extra_kwargs = _est_extra_kwargs

    def update(self, instance, validated_data):
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance

# Construction correcte de extra_kwargs pour CaptainSerializer
_cap_fields = ('user', 'experience', 'average_rating', 'total_trips', 'wallet_balance',
              'is_available', 'current_location', 'license_number', 'license_expiry_date',
              'years_of_experience', 'certifications', 'specializations', 'availability_status',
              'boat_photos', 'rate_per_km', 'rate_per_hour', 'boat')
_cap_extra_kwargs = {}
for field in _cap_fields:
    if field not in ['user', 'boat']:
        _cap_extra_kwargs[field] = {'required': False, 'allow_null': True}
        model_field = Captain._meta.get_field(field)
        if isinstance(model_field, (_models.CharField, _models.TextField)):
            _cap_extra_kwargs[field]['allow_blank'] = True

class BoatField(serializers.Field):
    """Champ personnalisé pour gérer le bateau en lecture et écriture"""

    def to_representation(self, value):
        """Lecture - retourne les données du bateau"""
        if value:
            return {
                'id': value.id,
                'name': value.name,
                'registration_number': value.registration_number,
                'boat_type': value.boat_type,
                'capacity': value.capacity,
                'color': value.color,
                'fuel_type': value.fuel_type,
                'fuel_consumption': value.fuel_consumption,
                'photos': value.photos,
                'zone_served': value.zone_served,
                'radius': value.radius,
                'is_available': value.is_available,
                'last_maintenance': value.last_maintenance,
                'next_maintenance': value.next_maintenance,
                'created_at': value.created_at,
                'updated_at': value.updated_at,
            }
        return None

    def to_internal_value(self, data):
        """Écriture - valide et retourne les données du bateau"""
        if not isinstance(data, dict):
            raise serializers.ValidationError("Les données du bateau doivent être un objet.")

        # Nettoyer les données avant validation
        cleaned_data = data.copy()

        # Nettoyer fuel_consumption - extraire seulement le nombre
        if 'fuel_consumption' in cleaned_data:
            fuel_value = cleaned_data['fuel_consumption']
            if isinstance(fuel_value, str):
                # Extraire le nombre de chaînes comme "10L/hour", "15.5 L/h", etc.
                import re
                match = re.search(r'(\d+(?:\.\d+)?)', fuel_value)
                if match:
                    cleaned_data['fuel_consumption'] = float(match.group(1))
                else:
                    # Si aucun nombre trouvé, supprimer le champ pour éviter l'erreur
                    cleaned_data.pop('fuel_consumption', None)

        return cleaned_data

class CaptainSerializer(serializers.ModelSerializer):
    user = UserSerializer(required=False)
    boat = BoatField(required=False)

    class Meta:
        model = Captain
        fields = _cap_fields + ('user', 'boat')
        extra_kwargs = _cap_extra_kwargs

    def update(self, instance, validated_data):
        # Mise à jour du profil utilisateur
        user_data = validated_data.pop('user', None)
        if user_data:
            for attr, value in user_data.items():
                setattr(instance.user, attr, value)
            instance.user.save()

        # Mise à jour du bateau du capitaine
        boat_data = validated_data.pop('boat', None)
        if boat_data and isinstance(boat_data, dict):
            from boats.models import Boat
            boat = getattr(instance, 'boat', None)
            if boat:
                # Mettre à jour le bateau existant
                for attr, value in boat_data.items():
                    if hasattr(boat, attr):  # Vérifier que l'attribut existe
                        setattr(boat, attr, value)
                boat.save()
                print(f"Bateau mis à jour: {boat.name}")
            else:
                # Créer un nouveau bateau si il n'existe pas
                boat_data['captain'] = instance
                try:
                    new_boat = Boat.objects.create(**boat_data)
                    print(f"Nouveau bateau créé: {new_boat.name}")
                except Exception as e:
                    print(f"Erreur création bateau: {e}")

        # Mise à jour des champs du capitaine lui-même
        for attr, value in validated_data.items():
            if hasattr(instance, attr):  # Vérifier que l'attribut existe
                setattr(instance, attr, value)
        instance.save()

        return instance



class UserProfileSerializer(serializers.ModelSerializer):
    """Sérialiseur pour afficher et modifier le profil utilisateur et ses sous-profils dynamiquement."""
    client_profile = ClientSerializer(source='client', required=False)
    captain_profile = CaptainSerializer(source='captain', required=False)
    establishment_profile = EstablishmentSerializer(source='establishment', required=False)
    profile_picture = serializers.URLField(allow_blank=True, required=False)

    class Meta:
        model = User
        fields = ('id', 'email', 'first_name', 'last_name', 'phone_number', 'type',
                  'profile_picture', 'client_profile', 'captain_profile', 'establishment_profile')
        read_only_fields = ('email', 'type')

    def update(self, instance, validated_data):
        client_data = validated_data.pop('client', None)
        captain_data = validated_data.pop('captain', None)
        establishment_data = validated_data.pop('establishment', None)
        # Permettre l’enregistrement direct d’une URL pour profile_picture
        profile_picture = validated_data.get('profile_picture')
        if profile_picture and isinstance(profile_picture, str):
            instance.profile_picture = profile_picture
            validated_data.pop('profile_picture', None)
        # Mise à jour des champs User
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        # Mise à jour du sous-profil selon le type
        if client_data and hasattr(instance, 'client'):
            client_serializer = ClientSerializer(instance.client, data=client_data, partial=True)
            if client_serializer.is_valid():
                client_serializer.save()
        if captain_data and hasattr(instance, 'captain'):
            captain_serializer = CaptainSerializer(instance.captain, data=captain_data, partial=True)
            if captain_serializer.is_valid():
                captain_serializer.save()
        if establishment_data and hasattr(instance, 'establishment'):
            establishment_serializer = EstablishmentSerializer(instance.establishment, data=establishment_data, partial=True)
            if establishment_serializer.is_valid():
                establishment_serializer.save()
        return instance
