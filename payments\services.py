from django.conf import settings
from django.utils import timezone
from decimal import Decimal
import stripe
import json
import logging

logger = logging.getLogger(__name__)

# Configuration de Stripe
stripe.api_key = settings.STRIPE_SECRET_KEY

class PaymentService:
    """
    Service centralisé pour gérer toutes les interactions de paiement.
    """
    
    @staticmethod
    def process_payment(user, amount, payment_method_id=None, description="", metadata=None, payment_type=None):
        """
        Traite un paiement avec Stripe ou via le portefeuille utilisateur.
        
        Args:
            user: L'utilisateur effectuant le paiement
            amount: Le montant à payer
            payment_method_id: ID de la méthode de paiement (Stripe) ou "wallet" pour utiliser le portefeuille
            description: Description du paiement
            metadata: Métadonnées additionnelles pour le paiement
            
        Returns:
            Dict avec les détails de la transaction
        """
        from .models import Transaction, Wallet
        
        # Convertir le montant en centimes pour Stripe
        amount_cents = int(Decimal(amount) * 100)
        
        # Vérifier si on utilise le portefeuille ou une carte
        if payment_method_id == "wallet":
            # Récupérer le portefeuille de l'utilisateur
            wallet, created = Wallet.objects.get_or_create(user=user)
            
            # Vérifier si le solde est suffisant
            if wallet.balance < Decimal(amount):
                raise ValueError("Solde insuffisant dans le portefeuille")
            
            # Débiter le portefeuille
            previous_balance = wallet.balance
            wallet.balance -= Decimal(amount)
            wallet.save()
            
            # Créer la transaction
            # Récupérer le portefeuille pour cette transaction
            wallet, created = Wallet.objects.get_or_create(user=user)
            
            transaction = Transaction.objects.create(
                wallet=wallet,
                type=Transaction.TransactionType.DEBIT,
                amount=amount,
                balance_after=wallet.balance,
                description=description,
                metadata=metadata if metadata else {}
            )
            
            return {
                "id": transaction.id,
                "amount": amount,
                "currency": "eur",
                "status": "succeeded",
                "created_at": transaction.created_at,
                "payment_method": "wallet",
                "wallet": {
                    "previous_balance": previous_balance,
                    "current_balance": wallet.balance
                }
            }
        else:
            # Paiement avec Stripe
            try:
                # Créer l'intention de paiement
                payment_intent = stripe.PaymentIntent.create(
                    amount=amount_cents,
                    currency="eur",
                    payment_method=payment_method_id,
                    confirm=True,
                    description=description,
                    metadata=metadata or {},
                    return_url=settings.PAYMENT_RETURN_URL,
                    customer=user.stripe_customer_id if hasattr(user, 'stripe_customer_id') else None
                )
                
                # Créer la transaction
                # Récupérer le portefeuille pour cette transaction
                wallet, created = Wallet.objects.get_or_create(user=user)
                
                # Créer un paiement
                from .models import Payment
                payment = Payment.objects.create(
                    user=user,
                    wallet=wallet,
                    amount=amount,
                    type=payment_type if payment_type else Payment.PaymentType.TRIP,  # Utiliser le type fourni ou une valeur par défaut sûre
                    payment_method=Payment.PaymentMethod.CARD,
                    status=Payment.Status.COMPLETED if payment_intent.status == 'succeeded' else Payment.Status.PENDING,
                    description=description,
                    stripe_payment_intent_id=payment_intent.id
                )
                
                # Créer la transaction associée
                transaction = Transaction.objects.create(
                    wallet=wallet,
                    payment=payment,
                    type=Transaction.TransactionType.DEBIT,
                    amount=amount,
                    balance_after=wallet.balance,
                    description=description,
                    metadata=metadata if metadata else {}
                )
                
                card = None
                if hasattr(payment_intent, 'payment_method') and payment_intent.payment_method:
                    payment_method = stripe.PaymentMethod.retrieve(payment_intent.payment_method)
                    if hasattr(payment_method, 'card'):
                        card = {
                            "brand": payment_method.card.brand,
                            "last4": payment_method.card.last4,
                            "exp_month": payment_method.card.exp_month,
                            "exp_year": payment_method.card.exp_year
                        }
                
                return {
                    "id": payment_intent.id,
                    "amount": amount,
                    "currency": "eur",
                    "status": payment_intent.status,
                    "created_at": timezone.now(),
                    "payment_method": "card",
                    "card": card
                }
            except stripe.error.CardError as e:
                # Le paiement a échoué
                error = e.json_body.get('error', {})
                raise ValueError(f"Paiement refusé: {error.get('message')}")
            except Exception as e:
                logger.error(f"Erreur de paiement: {str(e)}")
                raise ValueError(f"Erreur lors du traitement du paiement: {str(e)}")
    
    @staticmethod
    def refund_payment(transaction_id, amount=None, reason=None):
        """
        Rembourse un paiement partiellement ou totalement.
        
        Args:
            transaction_id: ID de la transaction à rembourser
            amount: Montant à rembourser (None pour rembourser tout)
            reason: Raison du remboursement
            
        Returns:
            Dict avec les détails du remboursement
        """
        from .models import Transaction, Refund, Wallet
        
        # Récupérer la transaction
        try:
            transaction = Transaction.objects.get(transaction_id=transaction_id)
        except Transaction.DoesNotExist:
            raise ValueError("Transaction introuvable")
        
        # Vérifier si la transaction peut être remboursée
        if transaction.status != "succeeded":
            raise ValueError("Seules les transactions réussies peuvent être remboursées")
        
        # Déterminer le montant à rembourser
        refund_amount = amount if amount is not None else transaction.amount
        refund_amount_cents = int(Decimal(refund_amount) * 100)
        
        # Vérifier si le montant est valide
        if refund_amount <= 0 or refund_amount > transaction.amount:
            raise ValueError("Montant de remboursement invalide")
        
        # Effectuer le remboursement selon la méthode de paiement
        if transaction.payment_method == "wallet":
            # Créditer le portefeuille de l'utilisateur
            wallet, created = Wallet.objects.get_or_create(user=transaction.user)
            wallet.balance += Decimal(refund_amount)
            wallet.save()
            
            # Créer l'enregistrement de remboursement
            refund = Refund.objects.create(
                transaction=transaction,
                amount=refund_amount,
                reason=reason or "Remboursement automatique",
                status="succeeded"
            )
            
            return {
                "id": refund.id,
                "amount": refund_amount,
                "status": "succeeded",
                "created_at": refund.created_at
            }
        else:
            # Remboursement via Stripe
            try:
                # Créer le remboursement
                stripe_refund = stripe.Refund.create(
                    payment_intent=transaction.transaction_id,
                    amount=refund_amount_cents,
                    reason=reason or "requested_by_customer"
                )
                
                # Créer l'enregistrement de remboursement
                refund = Refund.objects.create(
                    transaction=transaction,
                    amount=refund_amount,
                    reason=reason or "Remboursement demandé",
                    status=stripe_refund.status,
                    refund_id=stripe_refund.id
                )
                
                return {
                    "id": stripe_refund.id,
                    "amount": refund_amount,
                    "status": stripe_refund.status,
                    "created_at": timezone.now()
                }
            except Exception as e:
                logger.error(f"Erreur de remboursement: {str(e)}")
                raise ValueError(f"Erreur lors du remboursement: {str(e)}")
    
    @staticmethod
    def get_wallet_balance(user):
        """
        Récupère le solde du portefeuille d'un utilisateur.
        
        Args:
            user: L'utilisateur dont on veut connaître le solde
            
        Returns:
            Solde du portefeuille
        """
        from .models import Wallet
        
        wallet, created = Wallet.objects.get_or_create(user=user)
        return wallet.balance
    
    @staticmethod
    def auto_refund_on_cancellation(booking_id, booking_type, reason=None):
        """
        Gère le remboursement automatique lors de l'annulation d'une réservation.
        
        Args:
            booking_id: ID de la réservation (course ou navette)
            booking_type: Type de réservation ('trip' ou 'shuttle')
            reason: Raison de l'annulation
            
        Returns:
            Dict avec les détails du remboursement ou None si aucun remboursement n'est effectué
        """
        from .models import Transaction
        from trips.models import Trip, Shuttle
        from trips.shared_payments import ShuttleBooking
        
        # Déterminer les règles de remboursement selon le type de réservation
        if booking_type == 'trip':
            try:
                trip = Trip.objects.get(id=booking_id)
                
                # Récupérer la transaction associée à ce trajet
                transaction = Transaction.objects.filter(
                    metadata__contains=f'"trip_id": {booking_id}'
                ).first()
                
                if not transaction:
                    return None
                
                # Déterminer le montant du remboursement selon les règles
                current_time = timezone.now()
                time_diff = trip.scheduled_start_time - current_time
                
                # Règles de remboursement pour les courses individuelles
                if time_diff.total_seconds() > 24 * 3600:  # Plus de 24h avant départ
                    refund_amount = transaction.amount  # Remboursement complet
                elif time_diff.total_seconds() > 6 * 3600:  # Entre 6h et 24h avant départ
                    refund_amount = transaction.amount * Decimal('0.75')  # 75% de remboursement
                elif time_diff.total_seconds() > 2 * 3600:  # Entre 2h et 6h avant départ
                    refund_amount = transaction.amount * Decimal('0.50')  # 50% de remboursement
                elif time_diff.total_seconds() > 1 * 3600:  # Entre 1h et 2h avant départ
                    refund_amount = transaction.amount * Decimal('0.25')  # 25% de remboursement
                else:  # Moins d'1h avant départ
                    return None  # Pas de remboursement
                
                # Effectuer le remboursement
                if refund_amount > 0:
                    return PaymentService.refund_payment(
                        transaction_id=transaction.transaction_id,
                        amount=refund_amount,
                        reason=reason or f"Annulation de la course #{trip.id}"
                    )
                
            except Trip.DoesNotExist:
                return None
            
        elif booking_type == 'shuttle':
            try:
                booking = ShuttleBooking.objects.get(id=booking_id)
                shuttle = booking.shuttle
                
                # Récupérer la transaction associée à cette réservation
                transaction = Transaction.objects.filter(
                    metadata__contains=f'"shuttle_booking_id": {booking_id}'
                ).first()
                
                if not transaction:
                    return None
                
                # Déterminer le montant du remboursement selon les règles
                current_time = timezone.now()
                time_diff = shuttle.departure_time - current_time
                
                # Règles de remboursement pour les navettes (plus strictes)
                if time_diff.total_seconds() > 48 * 3600:  # Plus de 48h avant départ
                    refund_amount = transaction.amount  # Remboursement complet
                elif time_diff.total_seconds() > 24 * 3600:  # Entre 24h et 48h avant départ
                    refund_amount = transaction.amount * Decimal('0.75')  # 75% de remboursement
                elif time_diff.total_seconds() > 12 * 3600:  # Entre 12h et 24h avant départ
                    refund_amount = transaction.amount * Decimal('0.50')  # 50% de remboursement
                elif time_diff.total_seconds() > 6 * 3600:  # Entre 6h et 12h avant départ
                    refund_amount = transaction.amount * Decimal('0.25')  # 25% de remboursement
                else:  # Moins de 6h avant départ
                    return None  # Pas de remboursement
                
                # Effectuer le remboursement
                if refund_amount > 0:
                    result = PaymentService.refund_payment(
                        transaction_id=transaction.transaction_id,
                        amount=refund_amount,
                        reason=reason or f"Annulation de la réservation de navette #{booking.id}"
                    )
                    
                    # Mettre à jour le statut de la réservation
                    booking.status = 'REFUNDED'
                    booking.save()
                    
                    # Libérer les places sur la navette
                    shuttle.current_bookings -= booking.number_of_seats
                    shuttle.save()
                    
                    return result
                
            except ShuttleBooking.DoesNotExist:
                return None
        
        return None
