# Guide d'utilisation du système RAG Commodore

## Introduction

Ce guide explique comment utiliser efficacement le système RAG (Retrieval Augmented Generation) de Commodore dans vos applications. Le système RAG permet d'intégrer un chatbot intelligent capable de répondre aux questions des utilisateurs en se basant sur la documentation Commodore.

## Prérequis

Avant d'utiliser le système RAG, assurez-vous que :

1. <PERSON><PERSON> avez accès à l'API Commodore
2. Vous disposez d'un token d'authentification JWT valide
3. Redis est correctement configuré et démarré
4. Les documents nécessaires sont chargés dans la base de connaissances

## Intégration dans une application

### 1. Authentification

Toutes les requêtes au système RAG nécessitent une authentification par token JWT. Pour obtenir un token :

```javascript
// Exemple avec JavaScript/Fetch
async function getToken(email, password) {
  const response = await fetch('https://api.commodore.com/api/auth/token/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      email: email,
      password: password
    })
  });
  
  const data = await response.json();
  return data.access;
}
```

### 2. Création d'une session

Chaque conversation avec le chatbot doit être associée à une session :

```javascript
async function createSession(token, title) {
  const response = await fetch('https://api.commodore.com/api/rag/sessions/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      title: title
    })
  });
  
  const data = await response.json();
  return data.id;
}
```

### 3. Envoi d'un message et réception d'une réponse

Pour envoyer un message au chatbot et recevoir une réponse :

```javascript
async function sendMessage(token, sessionId, message, profile = 'Client') {
  const response = await fetch('https://api.commodore.com/api/rag/chat/api/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      session_id: sessionId,
      message: message,
      profile: profile
    })
  });
  
  return await response.json();
}
```

### 4. Récupération de l'historique des messages

Pour récupérer l'historique des messages d'une session :

```javascript
async function getMessages(token, sessionId) {
  const response = await fetch(`https://api.commodore.com/api/rag/sessions/${sessionId}/messages/`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  return await response.json();
}
```

### 5. Support hors ligne

Pour les applications mobiles, vous pouvez récupérer des données pour le support hors ligne :

```javascript
async function getOfflineData(token, profile = 'Client') {
  const response = await fetch(`https://api.commodore.com/api/rag/offline/data/?profile=${profile}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  return await response.json();
}
```

## Bonnes pratiques

### Gestion des sessions

1. **Une session par conversation** : Créez une nouvelle session pour chaque nouvelle conversation.
2. **Réutilisation des sessions** : Pour continuer une conversation existante, réutilisez l'ID de session.
3. **Limite de messages** : Si une session contient plus de 20 messages, envisagez d'en créer une nouvelle pour maintenir des performances optimales.

### Optimisation des performances

1. **Cache Redis** : Le système utilise Redis pour mettre en cache les réponses fréquentes. Assurez-vous que Redis est correctement configuré et démarré.
2. **Données hors ligne** : Pour les applications mobiles, téléchargez régulièrement les données hors ligne pour permettre un fonctionnement sans connexion.
3. **Limitation des requêtes** : Évitez d'envoyer plus de 10 requêtes par minute pour ne pas surcharger le serveur.

### Amélioration de la qualité des réponses

1. **Profil utilisateur** : Spécifiez toujours le profil utilisateur (Client, Capitaine, Établissement) pour obtenir des réponses adaptées.
2. **Questions précises** : Encouragez les utilisateurs à poser des questions précises et spécifiques.
3. **Feedback** : Utilisez l'endpoint de feedback pour signaler les bonnes et mauvaises réponses.

## Exemples d'intégration

### Application React Native

```jsx
import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, Button, FlatList } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

const ChatScreen = () => {
  const [sessionId, setSessionId] = useState(null);
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState([]);
  const [token, setToken] = useState(null);
  
  useEffect(() => {
    // Récupérer le token depuis le stockage
    AsyncStorage.getItem('token').then(storedToken => {
      setToken(storedToken);
      
      // Récupérer ou créer une session
      AsyncStorage.getItem('sessionId').then(storedSessionId => {
        if (storedSessionId) {
          setSessionId(storedSessionId);
          fetchMessages(storedToken, storedSessionId);
        } else {
          createNewSession(storedToken);
        }
      });
    });
  }, []);
  
  const createNewSession = async (token) => {
    try {
      const response = await fetch('https://api.commodore.com/api/rag/sessions/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          title: 'Nouvelle conversation'
        })
      });
      
      const data = await response.json();
      setSessionId(data.id);
      AsyncStorage.setItem('sessionId', data.id);
    } catch (error) {
      console.error('Erreur lors de la création de la session:', error);
    }
  };
  
  const fetchMessages = async (token, sessionId) => {
    try {
      const response = await fetch(`https://api.commodore.com/api/rag/sessions/${sessionId}/messages/`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      const data = await response.json();
      setMessages(data);
    } catch (error) {
      console.error('Erreur lors de la récupération des messages:', error);
    }
  };
  
  const sendMessage = async () => {
    if (!message.trim()) return;
    
    // Ajouter le message de l'utilisateur à la liste
    const userMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: message,
      created_at: new Date().toISOString()
    };
    
    setMessages([...messages, userMessage]);
    setMessage('');
    
    try {
      const response = await fetch('https://api.commodore.com/api/rag/chat/api/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          session_id: sessionId,
          message: userMessage.content,
          profile: 'Client'
        })
      });
      
      const data = await response.json();
      
      // Ajouter la réponse du chatbot à la liste
      const botMessage = {
        id: Date.now().toString() + '1',
        role: 'assistant',
        content: data.response,
        created_at: new Date().toISOString()
      };
      
      setMessages([...messages, userMessage, botMessage]);
    } catch (error) {
      console.error('Erreur lors de l\'envoi du message:', error);
    }
  };
  
  return (
    <View style={{ flex: 1, padding: 16 }}>
      <FlatList
        data={messages}
        keyExtractor={item => item.id}
        renderItem={({ item }) => (
          <View style={{ 
            alignSelf: item.role === 'user' ? 'flex-end' : 'flex-start',
            backgroundColor: item.role === 'user' ? '#DCF8C6' : '#ECECEC',
            padding: 8,
            borderRadius: 8,
            marginVertical: 4,
            maxWidth: '80%'
          }}>
            <Text>{item.content}</Text>
          </View>
        )}
      />
      
      <View style={{ flexDirection: 'row', marginTop: 8 }}>
        <TextInput
          style={{ flex: 1, borderWidth: 1, borderColor: '#CCCCCC', borderRadius: 8, padding: 8 }}
          value={message}
          onChangeText={setMessage}
          placeholder="Tapez votre message..."
        />
        <Button title="Envoyer" onPress={sendMessage} />
      </View>
    </View>
  );
};

export default ChatScreen;
```

## Dépannage

### Problèmes courants

1. **Erreur d'authentification** : Vérifiez que votre token JWT est valide et n'a pas expiré.
2. **Temps de réponse lent** : Vérifiez que Redis est correctement configuré et démarré.
3. **Réponses non pertinentes** : Assurez-vous que les documents nécessaires sont chargés dans la base de connaissances.
4. **Erreur de connexion** : Vérifiez votre connexion Internet et l'URL de l'API.

### Support

Pour toute assistance supplémentaire, contactez l'équipe de support Commodore à <EMAIL>.

## Conclusion

Le système RAG de Commodore offre une solution puissante pour intégrer un chatbot intelligent dans vos applications. En suivant les bonnes pratiques décrites dans ce guide, vous pouvez offrir à vos utilisateurs une expérience de support client de haute qualité, avec des réponses précises et personnalisées.
