<div align="center">
  <img src="https://img.icons8.com/fluency/96/sailing-ship.png" width="96" alt="Commodore logo"/>
  <h1>Commodore</h1>
  <p><b>Plateforme moderne et sécurisée pour la gestion et le paiement de trajets, navettes, maintenances et services nautiques.</b></p>
  <p>
    <img src="https://img.shields.io/badge/python-3.9%2B-blue?logo=python"/>
    <img src="https://img.shields.io/badge/stripe-payments-blueviolet?logo=stripe"/>
    <img src="https://img.shields.io/badge/api-restful-success?logo=swagger"/>
    <img src="https://img.shields.io/badge/tests-passing-brightgreen?logo=pytest"/>
    <img src="https://img.shields.io/badge/license-MIT-green"/>
  </p>
</div>

---

## ✨ Fonctionnalités principales

- 🔒 Paiement sécurisé (Stripe) pour :
  - 🚗 Courses individuelles
  - 🚌 Navettes
  - 🛠️ Maintenance bateaux
  - 📢 Promotions
  - 🤝 Paiement partagé (invitation)
- 👛 Gestion de portefeuille utilisateur
- 🔔 Notifications en temps réel (in-app et email)
- 💸 Remboursements, crédits, offsets carbone
- 📚 API RESTful complète et documentée
- 🛡️ Permissions et sécurité avancées

---

## 🚀 Démarrage rapide

```bash
# 1. Cloner le repo
https://github.com/votre-utilisateur/commodore.git
cd commodore

# 2. Créer et activer un environnement virtuel
python -m venv venv
source venv/bin/activate  # ou venv\Scripts\activate sous Windows

# 3. Installer les dépendances
pip install -r requirements.txt

# 4. Configurer les variables d'environnement
cp .env.example .env

# 5. Lancer la base de données et le serveur
python manage.py migrate
python manage.py runserver
```

---

## 🖥️ Aperçu visuel

| ![Sailing Ship](https://img.icons8.com/fluency/48/sailing-ship.png) | ![Wallet](https://img.icons8.com/fluency/48/wallet-app.png) | ![Credit Card](https://img.icons8.com/fluency/48/bank-cards.png) | ![Notification](https://img.icons8.com/fluency/48/appointment-reminders.png) | ![Teamwork](https://img.icons8.com/fluency/48/conference-call.png) | ![Repair](https://img.icons8.com/fluency/48/maintenance.png) | ![Promotion](https://img.icons8.com/fluency/48/advertising.png) | ![Carbon Offset](https://img.icons8.com/fluency/48/earth-planet.png) |
|:---------------------------------------------------------------:|:----------------------------------------------------------:|:-------------------------------------------------------------:|:---------------------------------------------------------------------:|:--------------------------------------------------------------:|:----------------------------------------------------------:|:----------------------------------------------------------:|:----------------------------------------------------------:|
| **Gestion bateaux** | **Portefeuille** | **Paiement** | **Notifications** | **Paiement partagé** | **Maintenance** | **Promotion** | **Offset carbone** |

---

## 📚 Documentation API

- Toute la documentation détaillée des endpoints se trouve dans [`payments/endpoints.txt`](payments/endpoints.txt).
- Exemple de requête :
```http
POST /api/payments/trips/1/pay/
Authorization: Bearer <token>
Content-Type: application/json
{
  "payment_method_id": "pm_xxxxx"
}
```

---

## 🔒 Sécurité

- Authentification JWT obligatoire pour toutes les routes sensibles
- Paiements traités via Stripe (PCI DSS compliant)
- Permissions fines selon le rôle (admin, capitaine, client, etc.)
- Aucune donnée sensible en dur dans le code

---

## 🤝 Contribution

Les PR sont bienvenues ! Pour contribuer :
1. Fork le repo
2. Crée une branche (`feature/ma-fonctionnalite`)
3. Ouvre une PR avec une description claire

---

## 🛠️ Stack technique

- Python 3.9+
- Django & Django REST Framework
- Stripe API
- PostgreSQL (ou SQLite pour dev)
- Docker (optionnel)

---

## 📄 Licence

MIT

---

<div align="center">
  <img src="https://img.icons8.com/fluency/48/sailing-ship.png" width="48" alt="Commodore"/>
  <br/>
  <b>Commodore — Naviguez vers l’excellence !</b>
</div>
