"""
Module d'initialisation pour les services RAG.

Ce module expose les fonctions principales du service RAG pour une utilisation externe.
"""

from ..models import ChatSession
from ..rag_service import RagService
from core.models import User

# Initialiser le service RAG
_rag_service = RagService()

def generate_response(user_message: str, user_profile: str = None) -> str:
    """
    Génère une réponse à un message utilisateur en utilisant le système RAG.

    Cette fonction est un wrapper autour de la méthode generate_response du service RAG,
    qui crée automatiquement une session de chat temporaire pour la génération de réponse.

    Args:
        user_message: Le message texte de l'utilisateur.
        user_profile: Le profil de l'utilisateur (Client, Établissement, Capitaine), optionnel.

    Returns:
        La réponse générée par le modèle.
    """
    # Trouver un utilisateur admin ou superuser pour la session
    admin_user = User.objects.filter(is_superuser=True).first()
    if not admin_user:
        admin_user = User.objects.first()  # Fallback si aucun admin n'est trouvé

    # Créer une session temporaire
    session = ChatSession.objects.create(
        user=admin_user,
        title=f"Support {user_profile or 'Utilisateur'}"
    )

    # Générer la réponse
    response = _rag_service.generate_response(session, user_message, user_profile)

    return response
