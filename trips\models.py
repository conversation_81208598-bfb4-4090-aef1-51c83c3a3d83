from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator
from accounts.models import Client, Captain, Establishment
from boats.models import Boat

class Trip(models.Model):
    class Status(models.TextChoices):
        PENDING = 'PENDING', _('En attente')
        ACCEPTED = 'ACCEPTED', _('Acceptée')
        REJECTED = 'REJECTED', _('Refusée')
        IN_PROGRESS = 'IN_PROGRESS', _('En cours')
        COMPLETED = 'COMPLETED', _('Terminée')
        CANCELLED = 'CANCELLED', _('Annulée')

    # Informations de base
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='trips')
    captain = models.ForeignKey(Captain, on_delete=models.CASCADE, related_name='trips')
    boat = models.ForeignKey(Boat, on_delete=models.CASCADE, related_name='trips')
    establishment = models.ForeignKey(Establishment, on_delete=models.CASCADE, related_name='trips', null=True, blank=True)

    # Détails du trajet
    start_location = models.CharField(_('lieu de départ'), max_length=255)
    end_location = models.CharField(_('lieu d\'arrivée'), max_length=255)
    scheduled_start_time = models.DateTimeField(_('heure de départ prévue'))
    scheduled_end_time = models.DateTimeField(_('heure d\'arrivée prévue'))
    actual_start_time = models.DateTimeField(_('heure de départ réelle'), null=True, blank=True)
    actual_end_time = models.DateTimeField(_('heure d\'arrivée réelle'), null=True, blank=True)

    # Détails des passagers
    passenger_count = models.IntegerField(_('nombre de passagers'), validators=[MinValueValidator(1)])
    passenger_names = models.JSONField(_('noms des passagers'), default=list)
    special_requests = models.TextField(_('demandes spéciales'), blank=True)

    # Statut et suivi
    status = models.CharField(_('statut'), max_length=20, choices=Status.choices, default=Status.PENDING)
    current_location = models.CharField(_('position actuelle'), max_length=255, blank=True)
    tracking_data = models.JSONField(_('données de suivi'), default=list)

    # Paiement
    base_price = models.DecimalField(_('prix de base'), max_digits=10, decimal_places=2)
    additional_charges = models.DecimalField(_('frais supplémentaires'), max_digits=10, decimal_places=2, default=0)
    tip = models.DecimalField(_('pourboire'), max_digits=10, decimal_places=2, default=0)
    total_price = models.DecimalField(_('prix total'), max_digits=10, decimal_places=2)
    payment_status = models.CharField(_('statut du paiement'), max_length=20, default='PENDING')
    payment_method = models.CharField(_('méthode de paiement'), max_length=50, blank=True)

    # Métadonnées
    created_at = models.DateTimeField(_('créé le'), auto_now_add=True)
    updated_at = models.DateTimeField(_('mis à jour le'), auto_now=True)
    cancellation_reason = models.TextField(_('raison d\'annulation'), blank=True)
    notes = models.TextField(_('notes'), blank=True)

    class Meta:
        verbose_name = _('course')
        verbose_name_plural = _('courses')
        ordering = ['-created_at']

    def __str__(self):
        return f'Course {self.id} - {self.start_location} → {self.end_location}'

    def calculate_total_price(self):
        self.total_price = self.base_price + self.additional_charges + self.tip
        self.save()

class Shuttle(models.Model):
    class Status(models.TextChoices):
        SCHEDULED = 'SCHEDULED', _('Programmée')
        IN_PROGRESS = 'IN_PROGRESS', _('En cours')
        COMPLETED = 'COMPLETED', _('Terminée')
        CANCELLED = 'CANCELLED', _('Annulée')

    establishment = models.ForeignKey(Establishment, on_delete=models.CASCADE, related_name='shuttles')
    boat = models.ForeignKey(Boat, on_delete=models.CASCADE, related_name='shuttles')
    captain = models.ForeignKey(Captain, on_delete=models.CASCADE, related_name='shuttles')

    route_name = models.CharField(_('nom de la route'), max_length=100)
    start_location = models.CharField(_('lieu de départ'), max_length=255)
    end_location = models.CharField(_('lieu d\'arrivée'), max_length=255)
    stops = models.JSONField(_('arrêts'), default=list)

    departure_time = models.DateTimeField(_('heure de départ'))
    arrival_time = models.DateTimeField(_('heure d\'arrivée'))
    frequency = models.CharField(_('fréquence'), max_length=50, blank=True)
    days_of_week = models.JSONField(_('jours de la semaine'), default=list)

    max_capacity = models.IntegerField(_('capacité maximale'))
    current_bookings = models.IntegerField(_('réservations actuelles'), default=0)
    price_per_person = models.DecimalField(_('prix par personne'), max_digits=10, decimal_places=2)

    status = models.CharField(_('statut'), max_length=20, choices=Status.choices, default=Status.SCHEDULED)
    is_recurring = models.BooleanField(_('récurrent'), default=False)
    cancellation_policy = models.TextField(_('politique d\'annulation'), blank=True)

    created_at = models.DateTimeField(_('créé le'), auto_now_add=True)
    updated_at = models.DateTimeField(_('mis à jour le'), auto_now=True)

    class Meta:
        verbose_name = _('navette')
        verbose_name_plural = _('navettes')
        ordering = ['departure_time']

    def __str__(self):
        return f'Navette {self.route_name} - {self.departure_time.date()}'

class Location(models.Model):
    trip = models.ForeignKey(Trip, on_delete=models.CASCADE, related_name='locations')
    latitude = models.DecimalField(_('latitude'), max_digits=9, decimal_places=6)
    longitude = models.DecimalField(_('longitude'), max_digits=9, decimal_places=6)
    accuracy = models.FloatField(_('précision'), null=True, blank=True)
    speed = models.FloatField(_('vitesse'), null=True, blank=True)
    heading = models.FloatField(_('cap'), null=True, blank=True)
    altitude = models.FloatField(_('altitude'), null=True, blank=True)
    timestamp = models.DateTimeField(_('horodatage'), auto_now_add=True)

    class Meta:
        verbose_name = _('position')
        verbose_name_plural = _('positions')
        ordering = ['-timestamp']

    def __str__(self):
        return f'Position de {self.trip} à {self.timestamp}'
