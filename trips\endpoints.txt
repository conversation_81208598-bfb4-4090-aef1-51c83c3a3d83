# API Endpoints - Trips (Système de Gestion de Courses Commodore)

## 🚢 NOUVEAU SYSTÈME DE DEMANDES DE COURSES - TESTÉ ET FONCTIONNEL ✅

### Vue d'ensemble
Le système de gestion de courses Commodore permet aux clients de créer des demandes de courses et de recevoir automatiquement des devis de capitaines disponibles. Le système supporte trois types de courses :

1. **COURSE SIMPLE** - Trajet d'un point A à un point B avec tarification au kilomètre
2. **MISE À DISPOSITION** - Réservation par heures avec tarification horaire
3. **NAVETTES GRATUITES** - Navettes offertes par les établissements partenaires

---

## 1. COURSE SIMPLE (Distance-based pricing) ✅ TESTÉ

### POST /api/trips/requests/simple/
- **Description**: Créer une demande de course simple et obtenir les devis automatiquement
- **Auth Required**: <PERSON><PERSON> (CLIENT uniquement)
- **Status**: ✅ **FONCTIONNEL** - Testé avec succès
- **Request Body**:
```json
{
  "boat_type": "CLASSIC",
  "departure_location": {
    "city_name": "Cotonou, Bénin",
    "coordinates": {
      "latitude": 6.3654,
      "longitude": 2.4183,
      "altitude": 0,
      "accuracy": 5.2,
      "altitude_accuracy": 3.1,
      "heading": 275.4,
      "speed": 8.3
    },
    "timestamp": 1684157825000
  },
  "arrival_location": {
    "city_name": "Ouidah, <PERSON>énin",
    "coordinates": {
      "latitude": 6.3629,
      "longitude": 2.0852,
      "altitude": 0,
      "accuracy": 5.2,
      "altitude_accuracy": 3.1,
      "heading": 275.4,
      "speed": 8.3
    },
    "timestamp": 1684157825000
  },
  "passenger_count": 4,
  "scheduled_date": "2025-06-15",
  "scheduled_time": "14:30:00"
}
```

- **Response (201 Created)**:
```json
{
  "trip_request": {
    "id": 12,
    "departure_location": {
      "city_name": "Cotonou, Bénin",
      "coordinates": {
        "latitude": 6.3654,
        "longitude": 2.4183,
        "altitude": 0,
        "accuracy": 5.2,
        "altitude_accuracy": 3.1,
        "heading": 275.4,
        "speed": 8.3
      },
      "timestamp": 1684157825000
    },
    "arrival_location": {
      "city_name": "Ouidah, Bénin",
      "coordinates": {
        "latitude": 6.3629,
        "longitude": 2.0852,
        "altitude": 0,
        "accuracy": 5.2,
        "altitude_accuracy": 3.1,
        "heading": 275.4,
        "speed": 8.3
      },
      "timestamp": 1684157825000
    },
    "client": {
      "user": {
        "id": 60,
        "email": "<EMAIL>",
        "first_name": "Test",
        "last_name": "Client",
        "phone_number": "",
        "type": "",
        "profile_picture": "",
        "is_active": true
      },
      "wallet_balance": "0.00",
      "date_of_birth": null,
      "nationality": "",
      "preferred_language": "fr",
      "emergency_contact_name": "Contact d'urgence",
      "emergency_contact_phone": "+229 87654321"
    },
    "distance_km": "36.81",
    "boat_type": "CLASSIC",
    "trip_type": "SIMPLE",
    "status": "PENDING",
    "passenger_count": 4,
    "created_at": "2025-05-31T08:30:03.901536+02:00",
    "updated_at": "2025-05-31T08:30:03.908141+02:00",
    "expires_at": "2025-05-31T08:40:03.901536+02:00",
    "scheduled_date": "2025-06-15",
    "scheduled_time": "14:30:00"
  },
  "quotes": [
    {
      "id": 8,
      "captain_details": {
        "user": {
          "id": 82,
          "email": "<EMAIL>",
          "first_name": "Alpha",
          "last_name": "Capitaine",
          "phone_number": "",
          "type": "",
          "profile_picture": "",
          "is_active": true
        },
        "experience": "7 ans d'expérience maritime",
        "average_rating": "0.00",
        "total_trips": 0,
        "wallet_balance": "0.00",
        "is_available": true,
        "current_location": "",
        "license_number": "LIC001",
        "license_expiry_date": null,
        "years_of_experience": 0,
        "certifications": [],
        "specializations": [],
        "availability_status": "AVAILABLE",
        "boat_photos": [],
        "rate_per_km": "2.41",
        "rate_per_hour": "44.06"
      },
      "boat_details": {
        "id": 11,
        "name": "Alpha One",
        "registration_number": "BN0001",
        "boat_type": "CLASSIC",
        "capacity": 8,
        "color": "Rouge",
        "fuel_type": "GASOLINE",
        "fuel_consumption": "10.89",
        "photos": [],
        "zone_served": "Cotonou, Bénin",
        "radius": 35,
        "captain": {
          "id": 82,
          "user": {
            "id": 82,
            "email": "<EMAIL>",
            "first_name": "Alpha",
            "last_name": "Capitaine",
            "phone_number": "",
            "profile_picture": ""
          },
          "experience": "7 ans d'expérience maritime",
          "average_rating": "0.00",
          "total_trips": 0,
          "is_available": true,
          "license_number": "LIC001",
          "years_of_experience": 0,
          "rate_per_hour": "44.06"
        },
        "establishment": null,
        "is_available": true,
        "last_maintenance": null,
        "next_maintenance": null,
        "created_at": "2025-05-31T08:23:48.135479+02:00",
        "updated_at": "2025-05-31T08:23:48.135479+02:00",
        "maintenance_records": []
      },
      "base_price": "88.71",
      "distance_km": "36.81",
      "rate_used": "2.41",
      "captain_name": "Alpha Capitaine",
      "captain_rating": "4.50",
      "boat_name": "Alpha One",
      "boat_capacity": 8,
      "created_at": "2025-05-31T08:30:03.917808+02:00",
      "is_available": true,
      "trip_request": 12,
      "captain": 82,
      "boat": 11
    }
  ]
}
```

---

## 2. MISE À DISPOSITION (Hourly pricing) ✅ TESTÉ

### POST /api/trips/requests/hourly/
- **Description**: Créer une demande de mise à disposition et obtenir les devis
- **Auth Required**: Oui (CLIENT uniquement)
- **Status**: ✅ **FONCTIONNEL** - Testé avec succès
- **Request Body**:
```json
{
  "boat_type": "TOURISM",
  "departure_location": {
    "city_name": "Cotonou Port, Bénin",
    "coordinates": {
      "latitude": 6.3654,
      "longitude": 2.4183,
      "altitude": 0,
      "accuracy": 5.2,
      "altitude_accuracy": 3.1,
      "heading": 275.4,
      "speed": 8.3
    },
    "timestamp": 1684157825000
  },
  "arrival_location": {
    "city_name": "Cotonou Port, Bénin",
    "coordinates": {
      "latitude": 6.3654,
      "longitude": 2.4183,
      "altitude": 0,
      "accuracy": 5.2,
      "altitude_accuracy": 3.1,
      "heading": 275.4,
      "speed": 8.3
    },
    "timestamp": 1684157825000
  },
  "start_date": "2025-06-20",
  "duration_hours": 4,
  "passenger_count": 6
}
```

- **Response (201 Created)**:
```json
{
  "trip_request": {
    "id": 13,
    "departure_location": {
      "city_name": "Cotonou Port, Bénin",
      "coordinates": {
        "latitude": 6.3654,
        "longitude": 2.4183,
        "altitude": 0,
        "accuracy": 5.2,
        "altitude_accuracy": 3.1,
        "heading": 275.4,
        "speed": 8.3
      },
      "timestamp": 1684157825000
    },
    "arrival_location": {
      "city_name": "Cotonou Port, Bénin",
      "coordinates": {
        "latitude": 6.3654,
        "longitude": 2.4183,
        "altitude": 0,
        "accuracy": 5.2,
        "altitude_accuracy": 3.1,
        "heading": 275.4,
        "speed": 8.3
      },
      "timestamp": 1684157825000
    },
    "client": {
      "user": {
        "id": 60,
        "email": "<EMAIL>",
        "first_name": "Test",
        "last_name": "Client",
        "phone_number": "",
        "type": "",
        "profile_picture": "",
        "is_active": true
      },
      "wallet_balance": "0.00",
      "date_of_birth": null,
      "nationality": "",
      "preferred_language": "fr",
      "emergency_contact_name": "Contact d'urgence",
      "emergency_contact_phone": "+229 87654321"
    },
    "distance_km": "0.00",
    "boat_type": "TOURISM",
    "trip_type": "HOURLY",
    "status": "PENDING",
    "passenger_count": 6,
    "created_at": "2025-05-31T08:30:04.123456+02:00",
    "updated_at": "2025-05-31T08:30:04.123456+02:00",
    "expires_at": "2025-05-31T08:40:04.123456+02:00",
    "start_date": "2025-06-20",
    "duration_hours": 4
  },
  "quotes": [
    {
      "id": 9,
      "captain_details": {
        "user": {
          "id": 82,
          "email": "<EMAIL>",
          "first_name": "Alpha",
          "last_name": "Capitaine",
          "phone_number": "",
          "type": "",
          "profile_picture": "",
          "is_active": true
        },
        "experience": "7 ans d'expérience maritime",
        "average_rating": "0.00",
        "total_trips": 0,
        "wallet_balance": "0.00",
        "is_available": true,
        "current_location": "",
        "license_number": "LIC001",
        "license_expiry_date": null,
        "years_of_experience": 0,
        "certifications": [],
        "specializations": [],
        "availability_status": "AVAILABLE",
        "boat_photos": [],
        "rate_per_km": "2.41",
        "rate_per_hour": "44.06"
      },
      "boat_details": {
        "id": 11,
        "name": "Alpha One",
        "registration_number": "BN0001",
        "boat_type": "CLASSIC",
        "capacity": 8,
        "color": "Rouge",
        "fuel_type": "GASOLINE",
        "fuel_consumption": "10.89",
        "photos": [],
        "zone_served": "Cotonou, Bénin",
        "radius": 35,
        "captain": {
          "id": 82,
          "user": {
            "id": 82,
            "email": "<EMAIL>",
            "first_name": "Alpha",
            "last_name": "Capitaine",
            "phone_number": "",
            "profile_picture": ""
          },
          "experience": "7 ans d'expérience maritime",
          "average_rating": "0.00",
          "total_trips": 0,
          "is_available": true,
          "license_number": "LIC001",
          "years_of_experience": 0,
          "rate_per_hour": "44.06"
        },
        "establishment": null,
        "is_available": true,
        "last_maintenance": null,
        "next_maintenance": null,
        "created_at": "2025-05-31T08:23:48.135479+02:00",
        "updated_at": "2025-05-31T08:23:48.135479+02:00",
        "maintenance_records": []
      },
      "base_price": "176.24",
      "distance_km": "0.00",
      "rate_used": "44.06",
      "captain_name": "Alpha Capitaine",
      "captain_rating": "4.50",
      "boat_name": "Alpha One",
      "boat_capacity": 8,
      "created_at": "2025-05-31T08:30:04.132456+02:00",
      "is_available": true,
      "trip_request": 13,
      "captain": 82,
      "boat": 11
    }
  ]
}
```

---

## 3. ACCEPTATION DE DEVIS ✅ TESTÉ

### POST /api/trips/quotes/{quote_id}/accept/
- **Description**: Accepter un devis et créer la course officielle
- **Auth Required**: Oui (CLIENT propriétaire uniquement)
- **Status**: ✅ **FONCTIONNEL** - Testé avec succès
- **Request Body**: Aucun
- **Response (201 Created)**:
```json
{
  "message": "Devis accepté et course créée",
  "trip_id": 15,
  "trip": {
    "id": 15,
    "client": {
      "user": {
        "id": 60,
        "email": "<EMAIL>",
        "first_name": "Test",
        "last_name": "Client",
        "phone_number": "",
        "type": "",
        "profile_picture": "",
        "is_active": true
      },
      "wallet_balance": "0.00",
      "date_of_birth": null,
      "nationality": "",
      "preferred_language": "fr",
      "emergency_contact_name": "Contact d'urgence",
      "emergency_contact_phone": "+229 87654321"
    },
    "captain": {
      "user": {
        "id": 82,
        "email": "<EMAIL>",
        "first_name": "Alpha",
        "last_name": "Capitaine",
        "phone_number": "",
        "type": "",
        "profile_picture": "",
        "is_active": true
      },
      "experience": "7 ans d'expérience maritime",
      "average_rating": "0.00",
      "total_trips": 0,
      "wallet_balance": "0.00",
      "is_available": true,
      "current_location": "",
      "license_number": "LIC001",
      "license_expiry_date": null,
      "years_of_experience": 0,
      "certifications": [],
      "specializations": [],
      "availability_status": "AVAILABLE",
      "boat_photos": [],
      "rate_per_km": "2.41",
      "rate_per_hour": "44.06"
    },
    "boat": {
      "id": 11,
      "name": "Alpha One",
      "registration_number": "BN0001",
      "boat_type": "CLASSIC",
      "capacity": 8,
      "color": "Rouge",
      "fuel_type": "GASOLINE",
      "fuel_consumption": "10.89",
      "photos": [],
      "zone_served": "Cotonou, Bénin",
      "radius": 35,
      "captain": {
        "id": 82,
        "user": {
          "id": 82,
          "email": "<EMAIL>",
          "first_name": "Alpha",
          "last_name": "Capitaine",
          "phone_number": "",
          "profile_picture": ""
        },
        "experience": "7 ans d'expérience maritime",
        "average_rating": "0.00",
        "total_trips": 0,
        "is_available": true,
        "license_number": "LIC001",
        "years_of_experience": 0,
        "rate_per_hour": "44.06"
      },
      "establishment": null,
      "is_available": true,
      "last_maintenance": null,
      "next_maintenance": null,
      "created_at": "2025-05-31T08:23:48.135479+02:00",
      "updated_at": "2025-05-31T08:23:48.135479+02:00",
      "maintenance_records": []
    },
    "start_location": "Cotonou, Bénin",
    "end_location": "Ouidah, Bénin",
    "passenger_count": 4,
    "base_price": "88.71",
    "total_price": "88.71",
    "status": "ACCEPTED",
    "scheduled_start_time": "2025-06-15T14:30:00Z",
    "scheduled_end_time": "2025-06-15T16:30:00Z",
    "created_at": "2025-05-31T08:30:05.123456+02:00"
  }
}
```

---

## 4. GESTION DES DEMANDES

### GET /api/trips/requests/{id}/
- **Description**: Récupérer les détails d'une demande avec ses devis
- **Auth Required**: Oui (CLIENT propriétaire uniquement)
- **Response (200 OK)**:
```json
{
  "trip_request": {
    "id": 1,
    "trip_type": "SIMPLE",
    "status": "PENDING",
    "boat_type": "CLASSIC",
    "departure_location": {...},
    "arrival_location": {...},
    "passenger_count": 4,
    "distance_km": "36.81",
    "scheduled_date": "2025-06-15",
    "scheduled_time": "14:30:00",
    "created_at": "2025-05-31T10:30:00Z",
    "expires_at": "2025-05-31T10:40:00Z"
  },
  "quotes": [
    {
      "id": 1,
      "captain_name": "Alpha Capitaine",
      "captain_rating": "4.50",
      "boat_name": "Alpha One",
      "boat_capacity": 8,
      "base_price": "88.71",
      "distance_km": "36.81",
      "rate_used": "2.41",
      "is_available": true,
      "captain_details": {...},
      "boat_details": {...}
    }
  ]
}
```

### PATCH /api/trips/requests/{id}/
- **Description**: Mettre à jour le statut d'une demande
- **Auth Required**: Oui
- **Request Body** (pour les clients - annuler):
```json
{
  "status": "CANCELLED"
}
```
- **Response (200 OK)**:
```json
{
  "message": "Demande annulée"
}
```

---

## 5. NAVETTES GRATUITES (Establishments)

### POST /api/trips/requests/shuttle/
- **Description**: Créer une demande de navette gratuite
- **Auth Required**: Oui (CLIENT uniquement)
- **Request Body**:
```json
{
  "establishment": 1,
  "departure_location": {
    "city_name": "Aéroport de Cotonou, Bénin",
    "coordinates": {
      "latitude": 6.3572,
      "longitude": 2.3844,
      "altitude": 0,
      "accuracy": 5.2,
      "altitude_accuracy": 3.1,
      "heading": 275.4,
      "speed": 8.3
    },
    "timestamp": 1684157825000
  },
  "departure_date": "2025-06-25",
  "departure_time": "16:00:00",
  "passenger_count": 2,
  "message": "Vol Air France AF456 arrivant à 15h45"
}
```
- **Response (201 Created)**:
```json
{
  "trip_request": {
    "id": 3,
    "trip_type": "SHUTTLE",
    "status": "PENDING",
    "establishment": 1,
    "establishment_details": {
      "id": 1,
      "name": "Hôtel Marina",
      "type": "HOTEL",
      "address": "Avenue de la Marina, Cotonou"
    },
    "departure_date": "2025-06-25",
    "departure_time": "16:00:00",
    "passenger_count": 2,
    "message": "Vol Air France AF456 arrivant à 15h45",
    "distance_km": "12.50"
  },
  "message": "Demande de navette créée. L'établissement sera notifié.",
  "distance_to_establishment": "12.50 km"
}
```

### GET /api/trips/requests/shuttle/
- **Description**: Lister les demandes de navettes (pour les établissements)
- **Auth Required**: Oui (ESTABLISHMENT uniquement)
- **Response (200 OK)**:
```json
[
  {
    "id": 3,
    "trip_type": "SHUTTLE",
    "status": "PENDING",
    "client": {
      "id": 1,
      "user": {
        "first_name": "Jean",
        "last_name": "Dupont",
        "email": "<EMAIL>"
      }
    },
    "departure_date": "2025-06-25",
    "departure_time": "16:00:00",
    "passenger_count": 2,
    "message": "Vol Air France AF456 arrivant à 15h45",
    "distance_km": "12.50"
  }
]
```

---

## 6. NETTOYAGE AUTOMATIQUE

### POST /api/trips/cleanup/expired/
- **Description**: Nettoyer les demandes expirées (endpoint administratif)
- **Auth Required**: Oui (STAFF uniquement)
- **Request Body**: Aucun
- **Response (200 OK)**:
```json
{
  "message": "5 demandes expirées nettoyées",
  "expired_count": 5
}
```

---

## 📋 RÈGLES DE GESTION DU SYSTÈME

### ⏰ Statuts des demandes:
- **PENDING**: En attente de réponse (10 minutes max)
- **ACCEPTED**: Acceptée (par établissement pour navettes, ou par acceptation de devis)
- **REJECTED**: Refusée (par établissement pour navettes)
- **IN_PROGRESS**: En cours (course commencée)
- **COMPLETED**: Terminée
- **CANCELLED**: Annulée (par le client)
- **EXPIRED**: Expirée (après 10 minutes automatiquement)

### ⏰ Expiration automatique:
- **Toutes les demandes expirent après 10 minutes**
- **Les devis associés deviennent indisponibles**
- **Nettoyage via l'endpoint `/cleanup/expired/`**

### 💰 Calcul des prix:
- **Course simple**: `distance_km × rate_per_km`
- **Mise à disposition**: `duration_hours × rate_per_hour`
- **Navette gratuite**: Gratuit (0€)

### 🔐 Permissions par type d'utilisateur:
- **CLIENT**:
  - ✅ Créer des demandes de courses
  - ✅ Voir ses demandes
  - ✅ Accepter des devis
  - ✅ Annuler ses demandes
- **CAPTAIN**:
  - ✅ Reçoit automatiquement des demandes de devis selon ses critères
  - ✅ Ses tarifs sont utilisés pour les calculs automatiques
- **ESTABLISHMENT**:
  - ✅ Voir les demandes de navettes qui lui sont adressées
  - ✅ Accepter/refuser les demandes de navettes
- **STAFF**:
  - ✅ Nettoyer les demandes expirées

### 🚢 Types de bateaux supportés:
- **CLASSIC**: Bateau classique
- **FISHING**: Bateau de pêche
- **TOURISM**: Bateau de tourisme
- **CARGO**: Bateau de transport de marchandises
- **SPEED**: Bateau rapide
- **LUXURY**: Bateau de luxe

### 📍 Structure de localisation requise:
```json
{
  "city_name": "Nom de la ville",
  "coordinates": {
    "latitude": 6.3654,
    "longitude": 2.4183,
    "altitude": 0,
    "accuracy": 5.2,
    "altitude_accuracy": 3.1,
    "heading": 275.4,
    "speed": 8.3
  },
  "timestamp": 1684157825000
}
```

### 🔄 Workflow complet:
1. **Client** crée une demande → Système calcule distance et génère devis automatiquement
2. **Client** reçoit liste des devis triés par prix
3. **Client** accepte un devis → Course officielle créée automatiquement
4. **Capitaine** reçoit notification de course acceptée
5. **Système** marque les autres devis comme indisponibles
6. **Expiration automatique** après 10 minutes si aucune action

---

## ✅ STATUT DU SYSTÈME

### 🎯 **FONCTIONNALITÉS TESTÉES ET OPÉRATIONNELLES:**
- ✅ **Course simple** - Calcul automatique de distance avec geopy
- ✅ **Mise à disposition** - Tarification horaire
- ✅ **Génération automatique de devis** - Basée sur les tarifs des capitaines
- ✅ **Acceptation de devis** - Création automatique de course officielle
- ✅ **Gestion des photos de profil** - Support des URLs AWS S3
- ✅ **Expiration automatique** - Après 10 minutes
- ✅ **Authentification JWT** - Sécurisation des endpoints
- ✅ **Permissions par rôle** - CLIENT, CAPTAIN, ESTABLISHMENT, STAFF

### 🚧 **FONCTIONNALITÉS À IMPLÉMENTER:**
- 🔄 **Navettes gratuites** - Système pour les établissements
- 🔄 **Suivi en temps réel** - WebSockets pour le tracking GPS
- 🔄 **Notifications push** - Alertes pour les changements de statut
- 🔄 **Système de paiement** - Intégration avec les portefeuilles
- 🔄 **Évaluations** - Système de notation des courses

---

## 🔗 ENDPOINTS PRINCIPAUX

| Endpoint | Méthode | Description | Status |
|----------|---------|-------------|--------|
| `/api/trips/requests/simple/` | POST | Course simple | ✅ |
| `/api/trips/requests/hourly/` | POST | Mise à disposition | ✅ |
| `/api/trips/requests/shuttle/` | POST | Navette gratuite | 🔄 |
| `/api/trips/quotes/{id}/accept/` | POST | Accepter devis | ✅ |
| `/api/trips/requests/{id}/` | GET | Détails demande | ✅ |
| `/api/trips/requests/{id}/` | PATCH | Modifier demande | ✅ |
| `/api/trips/cleanup/expired/` | POST | Nettoyage auto | ✅ |

**Le système de gestion de courses Commodore est maintenant opérationnel !** 🚢⚓
