# API Endpoints - Trips

## 1. Liste et création de courses
- **Endpoint**: GET /api/trips/
- **Description**: Récupérer la liste des courses filtrées par type d'utilisateur (client, capitaine, établissement)
- **Auth Required**: <PERSON><PERSON> (JWT <PERSON>)
- **Query Parameters**:
  - status: Filtrer par statut (PENDING, ACCEPTED, REJECTED, IN_PROGRESS, COMPLETED, CANCELLED)
  - date_from: Filtrer par date de début (format YYYY-MM-DD)
  - date_to: Filtrer par date de fin (format YYYY-MM-DD)
- **Response (200 OK)**:
```json
[
  {
    "id": 1,
    "status": "PENDING",
    "start_location": "Port de Saint-Tropez",
    "end_location": "Plage de Pampelonne",
    "scheduled_start_time": "2025-06-15T14:00:00Z",
    "scheduled_end_time": "2025-06-15T15:30:00Z",
    "passenger_count": 4,
    "total_price": "120.00",
    "captain": {
      "id": 5,
      "user": {
        "first_name": "<PERSON>",
        "last_name": "<PERSON><PERSON>"
      }
    },
    "boat": {
      "id": 3,
      "name": "Blue Wave",
      "photo": "https://example.com/boats/blue_wave.jpg"
    }
  },
  // ...
]
```

- **Endpoint**: POST /api/trips/
- **Description**: Créer une nouvelle course (réservée aux clients)
- **Auth Required**: Oui (JWT Token)
- **Request Body**:
```json
{
  "start_location": "Port de Saint-Tropez",
  "end_location": "Plage de Pampelonne",
  "scheduled_start_time": "2025-06-15T14:00:00Z",
  "scheduled_end_time": "2025-06-15T15:30:00Z",
  "passenger_count": 4,
  "passenger_names": ["John Doe", "Jane Doe", "Bob Smith", "Alice Smith"],
  "special_requests": "Besoin d'un accès facile pour une personne à mobilité réduite",
  "captain_id": 5,
  "boat_id": 3
}
```
- **Response (201 Created)**:
```json
{
  "id": 1,
  "status": "PENDING",
  "start_location": "Port de Saint-Tropez",
  "end_location": "Plage de Pampelonne",
  "scheduled_start_time": "2025-06-15T14:00:00Z",
  "scheduled_end_time": "2025-06-15T15:30:00Z",
  "passenger_count": 4,
  "passenger_names": ["John Doe", "Jane Doe", "Bob Smith", "Alice Smith"],
  "special_requests": "Besoin d'un accès facile pour une personne à mobilité réduite",
  "total_price": "120.00",
  "payment_status": "PENDING",
  "captain": {
    "id": 5,
    "user": {
      "first_name": "Jean",
      "last_name": "Dupont"
    }
  },
  "boat": {
    "id": 3,
    "name": "Blue Wave",
    "photo": "https://example.com/boats/blue_wave.jpg"
  }
}
```

## 2. Détails, modification et suppression d'une course
- **Endpoint**: GET /api/trips/{id}/
- **Description**: Récupérer les détails d'une course spécifique
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "id": 1,
  "status": "PENDING",
  "start_location": "Port de Saint-Tropez",
  "end_location": "Plage de Pampelonne",
  "scheduled_start_time": "2025-06-15T14:00:00Z",
  "scheduled_end_time": "2025-06-15T15:30:00Z",
  "actual_start_time": null,
  "actual_end_time": null,
  "passenger_count": 4,
  "passenger_names": ["John Doe", "Jane Doe", "Bob Smith", "Alice Smith"],
  "special_requests": "Besoin d'un accès facile pour une personne à mobilité réduite",
  "current_location": "",
  "tracking_data": [],
  "base_price": "100.00",
  "additional_charges": "20.00",
  "tip": "0.00",
  "total_price": "120.00",
  "payment_status": "PENDING",
  "payment_method": "",
  "client": {
    "id": 8,
    "user": {
      "first_name": "Marie",
      "last_name": "Martin",
      "email": "<EMAIL>",
      "phone_number": "+33612345678"
    }
  },
  "captain": {
    "id": 5,
    "user": {
      "first_name": "Jean",
      "last_name": "Dupont",
      "email": "<EMAIL>",
      "phone_number": "+33687654321"
    },
    "experience": "5 ans d'expérience en navigation"
  },
  "boat": {
    "id": 3,
    "name": "Blue Wave",
    "registration_number": "FR123456",
    "color": "Bleu",
    "capacity": 8,
    "fuel_type": "Diesel",
    "fuel_consumption": 12.5,
    "photo": "https://example.com/boats/blue_wave.jpg"
  },
  "establishment": null,
  "created_at": "2025-05-25T10:30:45Z",
  "updated_at": "2025-05-25T10:30:45Z"
}
```

- **Endpoint**: PATCH /api/trips/{id}/
- **Description**: Modifier une course existante (certains champs selon le type d'utilisateur)
- **Auth Required**: Oui (JWT Token)
- **Request Body** (exemple pour un capitaine acceptant une course):
```json
{
  "status": "ACCEPTED"
}
```
- **Response (200 OK)**:
```json
{
  "id": 1,
  "status": "ACCEPTED",
  "start_location": "Port de Saint-Tropez",
  "end_location": "Plage de Pampelonne",
  "scheduled_start_time": "2025-06-15T14:00:00Z",
  "scheduled_end_time": "2025-06-15T15:30:00Z",
  "actual_start_time": null,
  "actual_end_time": null,
  "passenger_count": 4,
  "passenger_names": ["John Doe", "Jane Doe", "Bob Smith", "Alice Smith"],
  "special_requests": "Besoin d'un accès facile pour une personne à mobilité réduite",
  "total_price": "120.00",
  "payment_status": "PENDING",
  "created_at": "2025-06-14T10:30:00Z",
  "updated_at": "2025-06-14T11:15:00Z",
  "captain": {
    "id": 5,
    "user": {
      "id": 12,
      "first_name": "Jean",
      "last_name": "Dupont",
      "email": "<EMAIL>",
      "phone_number": "+33612345678"
    },
    "average_rating": 4.8,
    "total_trips": 157,
    "is_available": true
  },
  "boat": {
    "id": 3,
    "name": "Blue Wave",
    "photo": "https://example.com/boats/blue_wave.jpg",
    "capacity": 8,
    "model": "Sunseeker Predator 55",
    "year": 2020
  }
}
```

- **Endpoint**: DELETE /api/trips/{id}/
- **Description**: Annuler/supprimer une course (effectue une annulation plutôt qu'une suppression réelle)
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "message": "Course annulée avec succès"
}
```

## 3. Suivi d'une course en temps réel
- **Endpoint**: GET /api/trips/{id}/tracking/
- **Description**: Récupérer les données de suivi en temps réel d'une course
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "trip_id": 1,
  "status": "IN_PROGRESS",
  "current_location": {
    "latitude": 43.2756,
    "longitude": 6.6408,
    "accuracy": 5.0,
    "speed": 15.2,
    "heading": 125.7,
    "timestamp": "2025-06-15T14:15:23Z"
  },
  "route": [
    {
      "latitude": 43.2728,
      "longitude": 6.6378,
      "timestamp": "2025-06-15T14:00:15Z"
    },
    {
      "latitude": 43.2742,
      "longitude": 6.6392,
      "timestamp": "2025-06-15T14:07:45Z"
    },
    {
      "latitude": 43.2756,
      "longitude": 6.6408,
      "timestamp": "2025-06-15T14:15:23Z"
    }
  ],
  "estimated_arrival_time": "2025-06-15T15:25:00Z"
}
```

- **Endpoint**: POST /api/trips/{id}/tracking/
- **Description**: Mettre à jour la position actuelle d'une course (réservé aux capitaines)
- **Auth Required**: Oui (JWT Token)
- **Request Body**:
```json
{
  "latitude": 43.2756,
  "longitude": 6.6408,
  "accuracy": 5.0,
  "speed": 15.2,
  "heading": 125.7
}
```
- **Response (200 OK)**:
```json
{
  "message": "Position mise à jour avec succès"
}
```

## 4. Liste et création de navettes
- **Endpoint**: GET /api/shuttles/
- **Description**: Récupérer la liste des navettes filtrées par type d'utilisateur
- **Auth Required**: Oui (JWT Token)
- **Query Parameters**:
  - status: Filtrer par statut (SCHEDULED, IN_PROGRESS, COMPLETED, CANCELLED)
  - date_from: Filtrer par date de début (format YYYY-MM-DD)
  - date_to: Filtrer par date de fin (format YYYY-MM-DD)
- **Response (200 OK)**:
```json
[
  {
    "id": 1,
    "route_name": "Port-Plage Express",
    "start_location": "Port de Saint-Tropez",
    "end_location": "Plage de Pampelonne",
    "departure_time": "2025-06-15T11:00:00Z",
    "arrival_time": "2025-06-15T11:30:00Z",
    "max_capacity": 12,
    "current_bookings": 8,
    "price_per_person": "25.00",
    "status": "SCHEDULED",
    "establishment": {
      "id": 3,
      "name": "Hôtel Riviera"
    },
    "boat": {
      "id": 5,
      "name": "Navette Riviera"
    },
    "captain": {
      "id": 7,
      "user": {
        "first_name": "Paul",
        "last_name": "Durand"
      }
    }
  },
  // ...
]
```

- **Endpoint**: POST /api/shuttles/
- **Description**: Créer une nouvelle navette (réservé aux établissements)
- **Auth Required**: Oui (JWT Token)
- **Request Body**:
```json
{
  "route_name": "Port-Plage Express",
  "start_location": "Port de Saint-Tropez",
  "end_location": "Plage de Pampelonne",
  "stops": [
    {
      "name": "Port du Pilon",
      "time_offset": 10
    }
  ],
  "departure_time": "2025-06-15T11:00:00Z",
  "arrival_time": "2025-06-15T11:30:00Z",
  "max_capacity": 12,
  "price_per_person": "25.00",
  "is_recurring": true,
  "days_of_week": [1, 2, 3, 4, 5],
  "boat_id": 5,
  "captain_id": 7
}
```
- **Response (201 Created)**:
```json
{
  "id": 1,
  "route_name": "Port-Plage Express",
  "start_location": "Port de Saint-Tropez",
  "end_location": "Plage de Pampelonne",
  "stops": [
    {
      "name": "Port du Pilon",
      "time_offset": 10
    }
  ],
  "departure_time": "2025-06-15T11:00:00Z",
  "arrival_time": "2025-06-15T11:30:00Z",
  "max_capacity": 12,
  "current_bookings": 0,
  "price_per_person": "25.00",
  "status": "SCHEDULED",
  "is_recurring": true,
  "days_of_week": [1, 2, 3, 4, 5]
}
```

## 5. Détails, modification et suppression d'une navette
- **Endpoint**: GET /api/shuttles/{id}/
- **Description**: Récupérer les détails d'une navette spécifique
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "id": 1,
  "route_name": "Port-Plage Express",
  "start_location": "Port de Saint-Tropez",
  "end_location": "Plage de Pampelonne",
  "stops": [
    {
      "name": "Port du Pilon",
      "time_offset": 10
    }
  ],
  "departure_time": "2025-06-15T11:00:00Z",
  "arrival_time": "2025-06-15T11:30:00Z",
  "frequency": "",
  "days_of_week": [1, 2, 3, 4, 5],
  "max_capacity": 12,
  "current_bookings": 8,
  "price_per_person": "25.00",
  "status": "SCHEDULED",
  "is_recurring": true,
  "cancellation_policy": "Annulation gratuite jusqu'à 2 heures avant le départ",
  "establishment": {
    "id": 3,
    "name": "Hôtel Riviera",
    "address": "123 Boulevard de la Mer, 83990 Saint-Tropez"
  },
  "boat": {
    "id": 5,
    "name": "Navette Riviera",
    "capacity": 15
  },
  "captain": {
    "id": 7,
    "user": {
      "first_name": "Paul",
      "last_name": "Durand"
    }
  },
  "created_at": "2025-05-20T14:30:45Z",
  "updated_at": "2025-05-25T09:15:23Z"
}
```

- **Endpoint**: PATCH /api/shuttles/{id}/
- **Description**: Modifier une navette existante
- **Auth Required**: Oui (JWT Token)
- **Request Body**:
```json
{
  "max_capacity": 15,
  "price_per_person": "22.50",
  "status": "CANCELLED"
}
```
- **Response (200 OK)**:
```json
{
  "id": 1,
  "max_capacity": 15,
  "price_per_person": "22.50",
  "status": "CANCELLED",
  "message": "Navette mise à jour avec succès"
}
```

- **Endpoint**: DELETE /api/shuttles/{id}/
- **Description**: Supprimer une navette
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "message": "Navette supprimée avec succès"
}
```

## Fonctionnalités implémentées

1. ✅ **Gestion des courses individuelles** : Création, mise à jour, annulation et consultation des courses individuelles.

2. ✅ **Gestion des navettes** : Création, modification et suppression de navettes régulières.

3. ✅ **Évaluation des courses** : Système permettant aux clients d'évaluer leurs courses terminées.

4. ✅ **Filtrage avancé** : Possibilité de filtrer les courses par statut, date, et type d'utilisateur.

5. ✅ **Intégration avec les paiements** : Liaison avec le système de paiement pour traiter les transactions liées aux courses.

## Fonctionnalités à implémenter

1. **Réservation de navettes** : API pour permettre aux clients de réserver des places sur une navette existante.

2. **Annulation partielle** : Permettre l'annulation partielle (quelques passagers) d'une réservation de navette.

3. **Remboursements automatiques** : Processus de remboursement automatique lors d'annulations de courses.

4. **WebSockets pour le suivi en temps réel** : Finaliser l'implémentation WebSocket pour le suivi en temps réel des courses et configurer les routes appropriées.

5. **Notifications en temps réel** : Améliorer l'intégration avec le système de notifications pour informer les utilisateurs des changements de statut des courses.

---

# NOUVEAUX ENDPOINTS - SYSTÈME DE DEMANDES DE COURSES

## 6. COURSE SIMPLE (Trajet immédiat ou programmé)

### POST /api/trips/requests/simple/
- **Description**: Créer une demande de course simple et obtenir les devis automatiquement
- **Auth Required**: Oui (CLIENT uniquement)
- **Request Body**:
```json
{
  "boat_type": "FISHING",
  "departure_location": {
    "city_name": "Cotonou, Bénin",
    "coordinates": {
      "latitude": 6.3654,
      "longitude": 2.4183,
      "altitude": 0,
      "accuracy": 5.2,
      "altitude_accuracy": 3.1,
      "heading": 275.4,
      "speed": 8.3
    },
    "timestamp": 1684157825000
  },
  "arrival_location": {
    "city_name": "Ouidah, Bénin",
    "coordinates": {
      "latitude": 6.3629,
      "longitude": 2.0852,
      "altitude": 0,
      "accuracy": 5.2,
      "altitude_accuracy": 3.1,
      "heading": 275.4,
      "speed": 8.3
    },
    "timestamp": 1684157825000
  },
  "passenger_count": 4,
  "scheduled_date": "2025-06-15",
  "scheduled_time": "14:30:00"
}
```
- **Response (201 Created)**:
```json
{
  "trip_request": {
    "id": 1,
    "trip_type": "SIMPLE",
    "status": "PENDING",
    "boat_type": "FISHING",
    "departure_location": {...},
    "arrival_location": {...},
    "passenger_count": 4,
    "distance_km": "25.30",
    "scheduled_date": "2025-06-15",
    "scheduled_time": "14:30:00",
    "created_at": "2025-05-31T10:30:00Z",
    "expires_at": "2025-05-31T10:40:00Z"
  },
  "quotes": [
    {
      "id": 1,
      "captain_name": "Capitaine Armel",
      "captain_rating": "4.8",
      "boat_name": "Sea Explorer",
      "boat_capacity": 8,
      "base_price": "37.95",
      "distance_km": "25.30",
      "rate_used": "1.50",
      "is_available": true,
      "captain_details": {...},
      "boat_details": {...}
    }
  ]
}
```

## 7. MISE À DISPOSITION (Réservation par heures)

### POST /api/trips/requests/hourly/
- **Description**: Créer une demande de mise à disposition et obtenir les devis
- **Auth Required**: Oui (CLIENT uniquement)
- **Request Body**:
```json
{
  "boat_type": "TOURISM",
  "departure_location": {
    "city_name": "Cotonou Port, Bénin",
    "coordinates": {
      "latitude": 6.3654,
      "longitude": 2.4183,
      "altitude": 0,
      "accuracy": 5.2,
      "altitude_accuracy": 3.1,
      "heading": 275.4,
      "speed": 8.3
    },
    "timestamp": 1684157825000
  },
  "arrival_location": {
    "city_name": "Cotonou Port, Bénin",
    "coordinates": {
      "latitude": 6.3654,
      "longitude": 2.4183,
      "altitude": 0,
      "accuracy": 5.2,
      "altitude_accuracy": 3.1,
      "heading": 275.4,
      "speed": 8.3
    },
    "timestamp": 1684157825000
  },
  "start_date": "2025-06-20",
  "duration_hours": 4,
  "passenger_count": 6
}
```
- **Response (201 Created)**:
```json
{
  "trip_request": {
    "id": 2,
    "trip_type": "HOURLY",
    "status": "PENDING",
    "boat_type": "TOURISM",
    "start_date": "2025-06-20",
    "duration_hours": 4,
    "passenger_count": 6,
    "distance_km": "0.00",
    "created_at": "2025-05-31T10:30:00Z",
    "expires_at": "2025-05-31T10:40:00Z"
  },
  "quotes": [
    {
      "id": 3,
      "captain_name": "Capitaine Jean",
      "captain_rating": "4.9",
      "boat_name": "Paradise Cruiser",
      "boat_capacity": 12,
      "base_price": "100.00",
      "distance_km": "0.00",
      "rate_used": "25.00",
      "is_available": true
    }
  ]
}
```

## 8. NAVETTES GRATUITES (Offertes par les partenaires)

### POST /api/trips/requests/shuttle/
- **Description**: Créer une demande de navette gratuite
- **Auth Required**: Oui (CLIENT uniquement)
- **Request Body**:
```json
{
  "establishment": 1,
  "departure_location": {
    "city_name": "Aéroport de Cotonou, Bénin",
    "coordinates": {
      "latitude": 6.3572,
      "longitude": 2.3844,
      "altitude": 0,
      "accuracy": 5.2,
      "altitude_accuracy": 3.1,
      "heading": 275.4,
      "speed": 8.3
    },
    "timestamp": 1684157825000
  },
  "departure_date": "2025-06-25",
  "departure_time": "16:00:00",
  "passenger_count": 2,
  "message": "Vol Air France AF456 arrivant à 15h45"
}
```
- **Response (201 Created)**:
```json
{
  "trip_request": {
    "id": 3,
    "trip_type": "SHUTTLE",
    "status": "PENDING",
    "establishment": 1,
    "establishment_details": {
      "id": 1,
      "name": "Hôtel Marina",
      "type": "HOTEL",
      "address": "Avenue de la Marina, Cotonou"
    },
    "departure_date": "2025-06-25",
    "departure_time": "16:00:00",
    "passenger_count": 2,
    "message": "Vol Air France AF456 arrivant à 15h45",
    "distance_km": "12.50"
  },
  "message": "Demande de navette créée. L'établissement sera notifié.",
  "distance_to_establishment": "12.50 km"
}
```

### GET /api/trips/requests/shuttle/
- **Description**: Lister les demandes de navettes (pour les établissements)
- **Auth Required**: Oui (ESTABLISHMENT uniquement)
- **Response (200 OK)**:
```json
[
  {
    "id": 3,
    "trip_type": "SHUTTLE",
    "status": "PENDING",
    "client": {
      "id": 1,
      "user": {
        "first_name": "Jean",
        "last_name": "Dupont",
        "email": "<EMAIL>"
      }
    },
    "departure_date": "2025-06-25",
    "departure_time": "16:00:00",
    "passenger_count": 2,
    "message": "Vol Air France AF456 arrivant à 15h45",
    "distance_km": "12.50"
  }
]
```

## 9. Gestion des demandes de courses

### GET /api/trips/requests/{id}/
- **Description**: Récupérer les détails d'une demande avec ses devis
- **Auth Required**: Oui (CLIENT propriétaire uniquement)
- **Response (200 OK)**:
```json
{
  "trip_request": {
    "id": 1,
    "trip_type": "SIMPLE",
    "status": "PENDING",
    "boat_type": "FISHING",
    "departure_location": {...},
    "arrival_location": {...},
    "passenger_count": 4,
    "distance_km": "25.30",
    "scheduled_date": "2025-06-15",
    "scheduled_time": "14:30:00",
    "created_at": "2025-05-31T10:30:00Z",
    "expires_at": "2025-05-31T10:40:00Z"
  },
  "quotes": [
    {
      "id": 1,
      "captain_name": "Capitaine Armel",
      "captain_rating": "4.8",
      "boat_name": "Sea Explorer",
      "boat_capacity": 8,
      "base_price": "37.95",
      "distance_km": "25.30",
      "rate_used": "1.50",
      "is_available": true,
      "captain_details": {...},
      "boat_details": {...}
    }
  ]
}
```

### PATCH /api/trips/requests/{id}/
- **Description**: Mettre à jour le statut d'une demande
- **Auth Required**: Oui
- **Request Body** (pour les clients - annuler):
```json
{
  "status": "CANCELLED"
}
```
- **Request Body** (pour les établissements - navettes):
```json
{
  "status": "ACCEPTED"
}
```
ou
```json
{
  "status": "REJECTED"
}
```
- **Response (200 OK)**:
```json
{
  "message": "Demande annulée"
}
```

## 10. Acceptation des devis

### POST /api/trips/quotes/{quote_id}/accept/
- **Description**: Accepter un devis et créer la course officielle
- **Auth Required**: Oui (CLIENT propriétaire uniquement)
- **Request Body**: Aucun
- **Response (201 Created)**:
```json
{
  "message": "Devis accepté et course créée",
  "trip_id": 15,
  "trip": {
    "id": 15,
    "client": {...},
    "captain": {...},
    "boat": {...},
    "start_location": "Cotonou, Bénin",
    "end_location": "Ouidah, Bénin",
    "passenger_count": 4,
    "base_price": "37.95",
    "total_price": "37.95",
    "status": "ACCEPTED",
    "scheduled_start_time": "2025-06-15T14:30:00Z",
    "scheduled_end_time": "2025-06-15T16:30:00Z",
    "created_at": "2025-05-31T10:35:00Z"
  }
}
```

## 11. Nettoyage automatique

### POST /api/trips/cleanup/expired/
- **Description**: Nettoyer les demandes expirées (endpoint administratif)
- **Auth Required**: Oui (STAFF uniquement)
- **Request Body**: Aucun
- **Response (200 OK)**:
```json
{
  "message": "5 demandes expirées nettoyées",
  "expired_count": 5
}
```

---

## RÈGLES DE GESTION DU NOUVEAU SYSTÈME

### Statuts des demandes:
- **PENDING**: En attente de réponse (10 minutes max)
- **ACCEPTED**: Acceptée (par établissement pour navettes, ou par acceptation de devis)
- **REJECTED**: Refusée (par établissement pour navettes)
- **IN_PROGRESS**: En cours (course commencée)
- **COMPLETED**: Terminée
- **CANCELLED**: Annulée (par le client)
- **EXPIRED**: Expirée (après 10 minutes automatiquement)

### Expiration automatique:
- ⏰ **Toutes les demandes expirent après 10 minutes**
- 🚫 **Les devis associés deviennent indisponibles**
- 🧹 **Nettoyage via l'endpoint `/cleanup/expired/`**

### Calcul des prix:
- **Course simple**: `distance_km × rate_per_km`
- **Mise à disposition**: `duration_hours × rate_per_hour`
- **Navette gratuite**: Gratuit (0€)

### Permissions par type d'utilisateur:
- **CLIENT**:
  - ✅ Créer des demandes de courses
  - ✅ Voir ses demandes
  - ✅ Accepter des devis
  - ✅ Annuler ses demandes
- **CAPTAIN**:
  - ✅ Reçoit automatiquement des demandes de devis selon ses critères
  - ✅ Ses tarifs sont utilisés pour les calculs automatiques
- **ESTABLISHMENT**:
  - ✅ Voir les demandes de navettes qui lui sont adressées
  - ✅ Accepter/refuser les demandes de navettes
- **STAFF**:
  - ✅ Nettoyer les demandes expirées

### Types de bateaux supportés:
- **FISHING**: Bateau de pêche
- **TOURISM**: Bateau de tourisme
- **CARGO**: Bateau de transport de marchandises
- **SPEED**: Bateau rapide
- **LUXURY**: Bateau de luxe

### Structure de localisation requise:
```json
{
  "city_name": "Nom de la ville",
  "coordinates": {
    "latitude": 6.3654,
    "longitude": 2.4183,
    "altitude": 0,
    "accuracy": 5.2,
    "altitude_accuracy": 3.1,
    "heading": 275.4,
    "speed": 8.3
  },
  "timestamp": 1684157825000
}
```

### Workflow complet:
1. **Client** crée une demande → Système calcule distance et génère devis automatiquement
2. **Client** reçoit liste des devis triés par prix
3. **Client** accepte un devis → Course officielle créée automatiquement
4. **Capitaine** reçoit notification de course acceptée
5. **Système** marque les autres devis comme indisponibles
6. **Expiration automatique** après 10 minutes si aucune action
