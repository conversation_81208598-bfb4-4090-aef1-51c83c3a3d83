#!/usr/bin/env python
"""
Script de test pour le nouveau système de gestion de courses
"""
import requests
import json

BASE_URL = 'http://localhost:8000/api'

def test_login():
    """Test de connexion"""
    login_data = {
        'email': '<EMAIL>',
        'password': 'testpass123'
    }
    response = requests.post(f'{BASE_URL}/login/', json=login_data)
    if response.status_code == 200:
        return response.json()['access_token']
    else:
        print(f"Erreur de connexion: {response.text}")
        return None

def test_simple_trip_request(token):
    """Test de création d'une course simple"""
    headers = {'Authorization': f'Bearer {token}'}

    trip_data = {
        "boat_type": "CLASSIC",
        "departure_location": {
            "city_name": "Cotonou, Bénin",
            "coordinates": {
                "latitude": 6.3654,
                "longitude": 2.4183,
                "altitude": 0,
                "accuracy": 5.2,
                "altitude_accuracy": 3.1,
                "heading": 275.4,
                "speed": 8.3
            },
            "timestamp": 1684157825000
        },
        "arrival_location": {
            "city_name": "Ouidah, Bénin",
            "coordinates": {
                "latitude": 6.3629,
                "longitude": 2.0852,
                "altitude": 0,
                "accuracy": 5.2,
                "altitude_accuracy": 3.1,
                "heading": 275.4,
                "speed": 8.3
            },
            "timestamp": 1684157825000
        },
        "passenger_count": 4,
        "scheduled_date": "2025-06-15",
        "scheduled_time": "14:30:00"
    }

    response = requests.post(f'{BASE_URL}/trips/requests/simple/', json=trip_data, headers=headers)
    print(f"Course simple - Status: {response.status_code}")

    if response.status_code == 201:
        result = response.json()
        print(f"✅ Demande créée: ID {result['trip_request']['id']}")
        print(f"✅ Distance calculée: {result['trip_request']['distance_km']} km")
        print(f"✅ Nombre de devis: {len(result['quotes'])}")

        if result['quotes']:
            best_quote = result['quotes'][0]
            print(f"✅ Meilleur prix: {best_quote['base_price']}€ par {best_quote['captain_name']}")
            return result['trip_request']['id'], result['quotes'][0]['id']
    else:
        print(f"❌ Erreur: {response.text}")
        return None, None

def test_hourly_trip_request(token):
    """Test de création d'une mise à disposition"""
    headers = {'Authorization': f'Bearer {token}'}

    trip_data = {
        "boat_type": "LUXE",
        "departure_location": {
            "city_name": "Cotonou Port, Bénin",
            "coordinates": {
                "latitude": 6.3654,
                "longitude": 2.4183,
                "altitude": 0,
                "accuracy": 5.2,
                "altitude_accuracy": 3.1,
                "heading": 275.4,
                "speed": 8.3
            },
            "timestamp": 1684157825000
        },
        "arrival_location": {
            "city_name": "Cotonou Port, Bénin",
            "coordinates": {
                "latitude": 6.3654,
                "longitude": 2.4183,
                "altitude": 0,
                "accuracy": 5.2,
                "altitude_accuracy": 3.1,
                "heading": 275.4,
                "speed": 8.3
            },
            "timestamp": 1684157825000
        },
        "start_date": "2025-06-20",
        "duration_hours": 4,
        "passenger_count": 6
    }

    response = requests.post(f'{BASE_URL}/trips/requests/hourly/', json=trip_data, headers=headers)
    print(f"Mise à disposition - Status: {response.status_code}")

    if response.status_code == 201:
        result = response.json()
        print(f"✅ Demande créée: ID {result['trip_request']['id']}")
        print(f"✅ Durée: {result['trip_request']['duration_hours']} heures")
        print(f"✅ Nombre de devis: {len(result['quotes'])}")
    else:
        print(f"❌ Erreur: {response.text}")

def test_accept_quote(token, quote_id):
    """Test d'acceptation d'un devis"""
    headers = {'Authorization': f'Bearer {token}'}

    response = requests.post(f'{BASE_URL}/trips/quotes/{quote_id}/accept/', headers=headers)
    print(f"Acceptation devis - Status: {response.status_code}")

    if response.status_code == 201:
        result = response.json()
        print(f"✅ Course créée: ID {result['trip_id']}")
        print(f"✅ Message: {result['message']}")
    else:
        print(f"❌ Erreur: {response.text}")

def test_base_endpoint(token):
    """Test de l'endpoint de base pour vérifier que les URLs fonctionnent"""
    headers = {'Authorization': f'Bearer {token}'}

    # Test de l'endpoint de base des trips
    response = requests.get(f'{BASE_URL}/trips/', headers=headers)
    print(f"Base trips endpoint - Status: {response.status_code}")

    if response.status_code == 200:
        print("✅ Endpoint de base accessible")
        return True
    else:
        print(f"❌ Erreur: {response.text}")
        return False

def main():
    print("🚢 Test du système de gestion de courses Commodore")
    print("=" * 50)

    # 1. Connexion
    print("\n1. Test de connexion...")
    token = test_login()
    if not token:
        print("❌ Impossible de se connecter")
        return
    print("✅ Connexion réussie")

    # 1.5. Test endpoint de base
    print("\n1.5. Test endpoint de base...")
    if not test_base_endpoint(token):
        print("❌ Problème avec les URLs de base")
        return

    # 2. Test course simple
    print("\n2. Test course simple...")
    trip_id, quote_id = test_simple_trip_request(token)

    # 3. Test mise à disposition
    print("\n3. Test mise à disposition...")
    test_hourly_trip_request(token)

    # 4. Test acceptation de devis
    if quote_id:
        print("\n4. Test acceptation de devis...")
        test_accept_quote(token, quote_id)

    print("\n🎉 Tests terminés !")

if __name__ == "__main__":
    main()
