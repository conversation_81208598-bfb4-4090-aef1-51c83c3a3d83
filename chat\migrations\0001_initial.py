# Generated by Django 4.2.8 on 2025-05-30 23:42

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("trips", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ChatRoom",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="nom")),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("TRIP", "Course"),
                            ("SUPPORT", "Support"),
                            ("GROUP", "Groupe"),
                        ],
                        max_length=20,
                        verbose_name="type",
                    ),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="actif")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="créé le"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="mis à jour le"),
                ),
                (
                    "participants",
                    models.ManyToManyField(
                        related_name="chat_rooms", to=settings.AUTH_USER_MODEL
                    ),
                ),
                (
                    "trip",
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="chat_room",
                        to="trips.trip",
                    ),
                ),
            ],
            options={
                "verbose_name": "salon de discussion",
                "verbose_name_plural": "salons de discussion",
                "ordering": ["-updated_at"],
            },
        ),
        migrations.CreateModel(
            name="Message",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("TEXT", "Texte"),
                            ("IMAGE", "Image"),
                            ("LOCATION", "Localisation"),
                            ("SYSTEM", "Système"),
                        ],
                        default="TEXT",
                        max_length=20,
                        verbose_name="type",
                    ),
                ),
                ("content", models.TextField(verbose_name="contenu")),
                (
                    "attachment",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to="chat_attachments/",
                        verbose_name="pièce jointe",
                    ),
                ),
                (
                    "metadata",
                    models.JSONField(default=dict, verbose_name="métadonnées"),
                ),
                ("is_read", models.BooleanField(default=False, verbose_name="lu")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="créé le"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="mis à jour le"),
                ),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="supprimé le"
                    ),
                ),
                (
                    "delivered_to",
                    models.ManyToManyField(
                        related_name="delivered_messages", to=settings.AUTH_USER_MODEL
                    ),
                ),
                (
                    "read_by",
                    models.ManyToManyField(
                        related_name="read_messages", to=settings.AUTH_USER_MODEL
                    ),
                ),
                (
                    "room",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="messages",
                        to="chat.chatroom",
                    ),
                ),
                (
                    "sender",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sent_messages",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "message",
                "verbose_name_plural": "messages",
                "ordering": ["created_at"],
            },
        ),
        migrations.CreateModel(
            name="ChatbotSession",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "session_id",
                    models.UUIDField(unique=True, verbose_name="ID de session"),
                ),
                ("context", models.JSONField(default=dict, verbose_name="contexte")),
                (
                    "last_interaction",
                    models.DateTimeField(
                        auto_now=True, verbose_name="dernière interaction"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="créé le"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="chatbot_sessions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "session chatbot",
                "verbose_name_plural": "sessions chatbot",
                "ordering": ["-last_interaction"],
            },
        ),
        migrations.CreateModel(
            name="ChatbotMessage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("USER", "Utilisateur"),
                            ("ASSISTANT", "Assistant"),
                            ("SYSTEM", "Système"),
                        ],
                        max_length=20,
                        verbose_name="rôle",
                    ),
                ),
                ("content", models.TextField(verbose_name="contenu")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="créé le"),
                ),
                (
                    "session",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="messages",
                        to="chat.chatbotsession",
                    ),
                ),
            ],
            options={
                "verbose_name": "message chatbot",
                "verbose_name_plural": "messages chatbot",
                "ordering": ["created_at"],
            },
        ),
    ]
