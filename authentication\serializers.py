from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.conf import settings
from .models import Verification<PERSON><PERSON>, UserSocialAccount
from accounts.models import Client, Captain, Establishment


User = get_user_model()

class UserTypeSerializer(serializers.Serializer):
    user_type = serializers.ChoiceField(choices=User.Types.choices)

class RegisterSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, min_length=8)
    user_type = serializers.ChoiceField(choices=User.Types.choices)
    name = serializers.CharField(required=True)  # Champ générique pour le nom

    class Meta:
        model = User
        fields = ('email', 'password', 'user_type', 'name')

    def create(self, validated_data):
        user_type = validated_data.pop('user_type')
        name = validated_data.pop('name')
        if user_type == User.Types.CLIENT:
            user = User.objects.create_user(email=validated_data['email'], password=validated_data['password'], type=user_type)
            Client.objects.create(user=user)
            user.first_name = name
            user.save()
        elif user_type == User.Types.CAPTAIN:
            user = User.objects.create_user(email=validated_data['email'], password=validated_data['password'], type=user_type)
            Captain.objects.create(user=user)
            user.first_name = name
            user.save()
        elif user_type == User.Types.ESTABLISHMENT:
            user = User.objects.create_user(email=validated_data['email'], password=validated_data['password'], type=user_type)
            Establishment.objects.create(user=user, name=name)
        else:
            raise serializers.ValidationError('Type d\'utilisateur inconnu')
        return user

# Les serializers spécifiques deviennent inutiles pour l'inscription, tout passe par RegisterSerializer
# Si tu veux garder des endpoints séparés, ils peuvent juste hériter sans rien ajouter.
        return user

class VerificationCodeSerializer(serializers.ModelSerializer):
    class Meta:
        model = VerificationCode
        fields = ('code',)

class SocialLoginSerializer(serializers.Serializer):
    provider = serializers.ChoiceField(choices=UserSocialAccount.Providers.choices)
    access_token = serializers.CharField()
    user_type = serializers.ChoiceField(choices=User.Types.choices, required=False)

    # Champs additionnels pour chaque type d'utilisateur
    # Client
    username = serializers.CharField(required=False)
    
    # Capitaine
    experience = serializers.CharField(required=False)
    boat = serializers.JSONField(required=False)
    
    # Établissement
    name = serializers.CharField(required=False)
    type = serializers.ChoiceField(choices=Establishment.Types.choices, required=False)
    address = serializers.CharField(required=False)
    description = serializers.CharField(required=False)
    main_photo = serializers.ImageField(required=False)
    secondary_photos = serializers.ListField(
        child=serializers.ImageField(),
        required=False
    )

class PasswordResetRequestSerializer(serializers.Serializer):
    email = serializers.EmailField(required=False)
    phone_number = serializers.CharField(required=False)

    def validate(self, attrs):
        if not attrs.get('email') and not attrs.get('phone_number'):
            raise serializers.ValidationError(
                "Email ou numéro de téléphone requis"
            )
        return attrs

class PasswordResetVerifySerializer(serializers.Serializer):
    email = serializers.EmailField(required=False)
    phone_number = serializers.CharField(required=False)
    code = serializers.CharField(min_length=6, max_length=6)
    new_password = serializers.CharField(min_length=8)

    def validate(self, attrs):
        if not attrs.get('email') and not attrs.get('phone_number'):
            raise serializers.ValidationError(
                "Email ou numéro de téléphone requis"
            )
        return attrs

class ResendVerificationCodeSerializer(serializers.Serializer):
    """Sérialiseur pour la demande de renvoi d'un code de vérification."""
    email = serializers.EmailField(required=True)
    
    def validate_email(self, value):
        try:
            user = User.objects.get(email=value)
            if user.is_active and user.email_verified:
                raise serializers.ValidationError("Ce compte est déjà vérifié.")
        except User.DoesNotExist:
            raise serializers.ValidationError("Aucun utilisateur trouvé avec cette adresse email.")
        return value
