# Script PowerShell pour résoudre définitivement les problèmes d'authentification
# Ce script va:
# 1. Supprimer la base de données
# 2. Recréer la base de données
# 3. Modifier settings.py pour une configuration propre
# 4. Faire les migrations
# 5. <PERSON><PERSON>er un superuser

Write-Host "Début de la réinitialisation complète du projet..." -ForegroundColor Green

# 1. Supprimer et recréer la base de données
Write-Host "Suppression de la base de données..." -ForegroundColor Yellow
$env:PGPASSWORD = "admin"  # Mot de passe PostgreSQL
psql -U postgres -c "DROP DATABASE IF EXISTS commodore_db;"
Write-Host "Création de la base de données..." -ForegroundColor Yellow
psql -U postgres -c "CREATE DATABASE commodore_db;"

# 2. Modifier settings.py pour une configuration propre
Write-Host "Modification de settings.py..." -ForegroundColor Yellow
$settingsPath = "D:\commodore\commodore\settings.py"
$settingsContent = Get-Content -Path $settingsPath -Raw

# Sauvegarder une copie du fichier settings.py original
Copy-Item -Path $settingsPath -Destination "$settingsPath.bak" -Force

# Remplacer les configurations d'authentification
$settingsContent = $settingsContent -replace "'allauth',`r?`n\s*'allauth.account',`r?`n\s*'allauth.socialaccount',`r?`n\s*'allauth.socialaccount.providers.facebook',`r?`n\s*'allauth.socialaccount.providers.google',`r?`n\s*'allauth.socialaccount.providers.apple',", ""
$settingsContent = $settingsContent -replace "'dj_rest_auth.registration',", ""

# Ajouter les configurations nécessaires
$settingsContent = $settingsContent -replace "# Authentication - Configuration simplifiée`r?`n\s*'rest_framework.authtoken',", "# Authentication - Configuration simplifiée`r`n    'rest_framework.authtoken',"

# Configurer REST_FRAMEWORK
$settingsContent = $settingsContent -replace "REST_FRAMEWORK = \{`r?`n\s*'DEFAULT_AUTHENTICATION_CLASSES': \(`r?`n\s*'rest_framework.authentication.TokenAuthentication',`r?`n\s*'rest_framework.authentication.SessionAuthentication',`r?`n\s*'rest_framework_simplejwt.authentication.JWTAuthentication',`r?`n\s*\),", "REST_FRAMEWORK = {`r`n    'DEFAULT_AUTHENTICATION_CLASSES': (`r`n        'rest_framework.authentication.TokenAuthentication',`r`n        'rest_framework.authentication.SessionAuthentication',`r`n        'rest_framework_simplejwt.authentication.JWTAuthentication',`r`n    ),"

# Écrire le contenu modifié
Set-Content -Path $settingsPath -Value $settingsContent

# 3. Modifier urls.py pour une configuration propre
Write-Host "Modification de urls.py..." -ForegroundColor Yellow
$urlsPath = "D:\commodore\commodore\urls.py"
$urlsContent = Get-Content -Path $urlsPath -Raw

# Sauvegarder une copie du fichier urls.py original
Copy-Item -Path $urlsPath -Destination "$urlsPath.bak" -Force

# Remplacer les configurations d'authentification
$urlsContent = $urlsContent -replace "path\('api/auth/registration/', include\('dj_rest_auth.registration.urls'\)\),", ""
$urlsContent = $urlsContent -replace "path\('api/auth/facebook/', include\('allauth.socialaccount.providers.facebook.urls'\)\),", ""
$urlsContent = $urlsContent -replace "path\('api/auth/google/', include\('allauth.socialaccount.providers.google.urls'\)\),", ""
$urlsContent = $urlsContent -replace "path\('api/auth/apple/', include\('allauth.socialaccount.providers.apple.urls'\)\),", ""

# Écrire le contenu modifié
Set-Content -Path $urlsPath -Value $urlsContent

# 4. Faire les migrations
Write-Host "Création des migrations..." -ForegroundColor Yellow
python manage.py makemigrations

Write-Host "Application des migrations..." -ForegroundColor Yellow
python manage.py migrate

# 5. Créer un superuser
Write-Host "Création d'un superuser..." -ForegroundColor Yellow
Write-Host "Pour créer un superuser, exécute la commande suivante:"
Write-Host "python manage.py createsuperuser" -ForegroundColor Cyan

Write-Host "Réinitialisation complète terminée !" -ForegroundColor Green
Write-Host "Tu peux maintenant utiliser ton API avec l'authentification par token." -ForegroundColor Green
