# API Endpoints - Authentication

## 1. Inscription (Register)
- **Endpoint**: POST /api/register/
- **Endpoint**: POST /api/verify-email/
- **Endpoint**: POST /api/resend-verification-code/
- **Endpoint**: POST /api/login/
- **Endpoint**: POST /api/password/reset/
- **Endpoint**: POST /api/password/reset/verify/
- **Endpoint**: POST /api/token/refresh/

# Endpoints détaillés

## 1. Inscription
- **URL**: POST /api/register/
- **Body** :
```json
{
  "email": "...",
  "password": "...",
  "user_type": "CLIENT|CAPTAIN|ESTABLISHMENT"
}
```

## 2. Vérification Email
- **URL**: POST /api/verify-email/
- **Body** :
```json
{
  "email": "...",
  "code": "123456"
}
```

## 3. Renvoyer code vérification
- **URL**: POST /api/resend-verification-code/
- **Body** :
```json
{
  "email": "..."
}
```

## 4. Connexion
- **URL**: POST /api/login/
- **Body** :
```json
{
  "email": "...",
  "password": "..."
}
```

## 5. Réinitialisation mot de passe (demande)
- **URL**: POST /api/password/reset/
- **Body** :
```json
{
  "email": "..."
}
```

## 6. Réinitialisation mot de passe (vérification code)
- **URL**: POST /api/password/reset/verify/
- **Body** :
```json
{
  "email": "...",
  "code": "123456",
  "new_password": "..."
}
```

## 7. Refresh Token
- **URL**: POST /api/token/refresh/
- **Body** :
```json
{
  "refresh": "<refresh_token>"
}
```

- **Description**: Inscription d'un nouvel utilisateur avec choix du type d'utilisateur et vérification par email
- **Request Body** (exemples pour chaque type) :

**CLIENT**
```json
{
  "name": "Client Test",
  "email": "<EMAIL>",
  "password": "...",
  "user_type": "CLIENT"
}
```

**CAPTAIN**
```json
{
  "name": "Captain Test",
  "email": "<EMAIL>",
  "password": "...",
  "user_type": "CAPTAIN"
}
```

**ESTABLISHMENT**
```json
{
  "name": "Etab Test",
  "email": "<EMAIL>",
  "password": "...",
  "user_type": "ESTABLISHMENT"
}
```


# Endpoints - Authentification

## 1. Inscription
- **Endpoint** : POST /api/register/
- **Description** : Inscription d'un nouvel utilisateur (client, capitaine ou établissement) avec uniquement nom, email et mot de passe. Le type d'utilisateur est à préciser.
- **Request Body** : voir exemples plus haut.

---

## Tests automatisés avec Insomnia

Pour tester automatiquement chaque type d'utilisateur dans Insomnia :

- Crée une requête POST vers `http://127.0.0.1:8000/api/register/`
- Mets le body JSON selon le type :

**CLIENT**
```json
{
  "name": "Client Test",
  "email": "<EMAIL>",
  "password": "...",
  "user_type": "CLIENT"
}
```

**CAPTAIN**
```json
{
  "name": "Captain Test",
  "email": "<EMAIL>",
  "password": "...",
  "user_type": "CAPTAIN"
}
```

**ESTABLISHMENT**
```json
{
  "name": "Etab Test",
  "email": "<EMAIL>",
  "password": "...",
  "user_type": "ESTABLISHMENT"
}
```

Tu peux dupliquer la requête dans Insomnia, changer l’email/le type, et automatiser les tests pour chaque profil.

---

  "email": "<EMAIL>",
  "password": "motdepasse123",
  "user_type": "CLIENT" // ou "CAPTAIN" ou "ESTABLISHMENT"
}
```
> Les champs spécifiques (ex: expérience, bateau, adresse, etc.) sont à compléter/modifier après connexion via PATCH sur le profil utilisateur.

- **Response (201 Created)** :
```json
{
  "message": "Inscription réussie. Veuillez vérifier votre email pour activer votre compte."
}
```

## 2. Connexion
- **Endpoint** : POST /api/auth/login/
- **Description** : Authentification avec email et mot de passe.
- **Request Body** :
```json
{
  "email": "<EMAIL>",
  "password": "motdepasse123"
}
```
- **Response (200 OK)** :
```json
{
  "access": "<jwt-token>",
  "refresh": "<refresh-token>"
}
```

## 3. Vérification d'email
- **Endpoint** : POST /api/auth/verify-email/
- **Description** : Vérifier l'adresse email avec un code reçu par email.
- **Request Body** :
```json
{
  "email": "<EMAIL>",
  "code": "123456"
}
```

## 4. Vérification de téléphone
- **Endpoint** : POST /api/auth/verify-phone/
- **Description** : Vérifier le numéro de téléphone avec un code reçu par SMS.
- **Request Body** :
```json
{
  "phone_number": "+33612345678",
  "code": "654321"
}
```

## 5. Connexion sociale
- **Endpoint** : POST /api/auth/social-login/
- **Description** : Connexion via Facebook, Google ou Apple.
- **Request Body** :
```json
{
  "provider": "GOOGLE",
  "access_token": "<token-oauth>",
  "user_type": "CLIENT" // ou "CAPTAIN", "ESTABLISHMENT"
}
```

## 6. Réinitialisation du mot de passe (en 2 étapes)
- **Demande** :
  - **Endpoint** : POST /api/auth/password-reset/request/
  - **Request Body** :
    ```json
    {
      "email": "<EMAIL>"
    }
    ```
- **Vérification & nouveau mot de passe** :
  - **Endpoint** : POST /api/auth/password-reset/verify/
  - **Request Body** :
    ```json
    {
      "email": "<EMAIL>",
      "code": "123456",
      "new_password": "nouveaumotdepasse"
    }
    ```

## 7. Résumé - Champs obligatoires à l'inscription
| Champ      | Obligatoire | Note |
|------------|-------------|------|
| name       | Oui         | Nom d'utilisateur ou nom d'établissement |
| email      | Oui         |     |
| password   | Oui         |     |
| user_type  | Oui         | CLIENT, CAPTAIN ou ESTABLISHMENT |
|            "boat": {
                "name": "Cascade Boat",
                "registration_number": "REG1234",
                "color": "Blanc",
                "capacity": 6,
                "fuel_type": "DIESEL",
                "fuel_consumption": 10.5,
                "photos": [
                    "https://bucket.s3.amazonaws.com/photo1.jpg",
                    "https://bucket.s3.amazonaws.com/photo2.jpg",
                    "https://bucket.s3.amazonaws.com/photo3.jpg",
                    "https://bucket.s3.amazonaws.com/photo4.jpg"
                ],
                "zone_served": "Porto-Novo, Cotonou, Ouidah",
                "radius": 25
            }
        }
    },
# Notes de validation API PATCH Boat :
- `photos` : liste de 0 à 4 liens (URLs). Chaque photo doit être un lien valide (ex : https://...)
- `radius` : entier compris entre 1 et 100 (km)
- `zone_served` : texte libre (zone géographique)

> Tous les autres champs sont à compléter ou modifier après connexion via PATCH sur le profil (voir endpoints comptes).

  "address": "23 boulevard de la Mer, 83700 Saint-Raphaël",
  "description": "Restaurant avec accès direct à la mer"
```
- **Response (201 Created)**:
```json
{
  "user_id": 123,
  "email": "<EMAIL>",
  "message": "Vérifiez votre email pour activer le compte"
}
```

## 2. Vérification d'Email
- **Endpoint**: POST /api/verify-email/
- **Description**: Vérifier l'email avec le code reçu
- **Request Body**:
```json
{
  "email": "<EMAIL>",
  "code": "1234"
}
```
- **Response (200 OK)**:
```json
{
  "message": "Email vérifié, compte activé",
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

## 3. Renvoyer le code de vérification d'Email
- **Endpoint**: POST /api/resend-verification-code/
- **Description**: Renvoyer un nouveau code de vérification à l'adresse email indiquée
- **Request Body**:
```json
{
  "email": "<EMAIL>"
}
```
- **Response (200 OK)**:
```json
{
  "message": "Code de vérification renvoyé avec succès"
}
```

## 4. Connexion sociale (Social Login)
- **Endpoint**: POST /api/social-login/
- **Description**: Connexion ou inscription via Facebook, Google, ou Apple
- **Request Body**:
```json
{
  "provider": "facebook", // ou "google", "apple"
  "access_token": "EAABZAWh...",
  "user_type": "CLIENT", // Requis si nouvel utilisateur
  // Autres champs spécifiques au type d'utilisateur si nouveau
}
```
- **Response (200 OK)**:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user_id": 123,
  "email": "<EMAIL>"
}
```

## 5. Connexion classique (Login)
- **Endpoint**: POST /api/login/
- **Description**: Connexion avec email et mot de passe
- **Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "motdepasse123"
}
```
- **Response (200 OK)**:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user_id": 123,
  "email": "<EMAIL>",
  "user_type": "CLIENT"
}
```

## 6. Rafraîchissement de Token (Token Refresh)
- **Endpoint**: POST /api/token/refresh/
- **Description**: Renouveler le token d'accès à l'aide du refresh token
- **Request Body**:
```json
{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```
- **Response (200 OK)**:
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

## 7. Demande de réinitialisation de mot de passe
- **Endpoint**: POST /api/password/reset/
- **Description**: Demande de réinitialisation de mot de passe
- **Request Body**:
```json
{
  "email": "<EMAIL>"
}
```
- **Response (200 OK)**:
```json
{
  "message": "Code de vérification envoyé par email"
}
```

## 8. Vérification et réinitialisation du mot de passe
- **Endpoint**: POST /api/password/reset/verify/
- **Description**: Vérification du code et mise à jour du mot de passe
- **Request Body**:
```json
{
  "email": "<EMAIL>",
  "code": "1234",
  "new_password": "nouveau_motdepasse123"
}
```
- **Response (200 OK)**:
```json
{
  "message": "Mot de passe réinitialisé"
}
```

## Fonctionnalités implémentées

1. ✅ **Inscription et création de compte** : Enregistrement de nouveaux utilisateurs avec différents types de profils (client, capitaine, établissement).

2. ✅ **Authentification par JWT** : Système d'authentification basé sur les tokens JWT.

3. ✅ **Vérification d'email** : Mécanisme d'envoi et vérification de codes pour confirmer les adresses email.

4. ✅ **Réinitialisation de mot de passe** : Procédure de récupération et réinitialisation des mots de passe oubliés.

5. ✅ **Gestion des erreurs d'authentification** : Traitement et retour d'erreurs appropriés lors des tentatives d'authentification infructueuses.

## Fonctionnalités à implémenter

1. **Rafraîchissement de token** : Mécanisme de rafraîchissement automatique des tokens JWT avant expiration.

2. **Validation renforcée des mots de passe** : Implémentation de règles plus strictes pour la sécurité des mots de passe.

3. **Authentification par SMS** : Alternative à l'email pour la vérification et la connexion via SMS.

4. **Déconnexion avec invalidation du token** : Mécanisme pour invalider les tokens JWT lors de la déconnexion.
