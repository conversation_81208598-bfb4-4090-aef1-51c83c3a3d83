# Generated by Django 4.2.8 on 2025-05-30 23:42

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="NotificationTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=100, unique=True, verbose_name="nom"),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("TRIP_REQUEST", "Demande de course"),
                            ("TRIP_ACCEPTED", "Course acceptée"),
                            ("TRIP_REJECTED", "Course refusée"),
                            ("TRIP_STARTED", "Course démarrée"),
                            ("TRIP_COMPLETED", "Course terminée"),
                            ("TRIP_CANCELLED", "Course annulée"),
                            ("PAYMENT_RECEIVED", "Paiement reçu"),
                            ("PAYMENT_FAILED", "Paiement échoué"),
                            ("REFUND_PROCESSED", "Remboursement traité"),
                            ("NEW_MESSAGE", "Nouveau message"),
                            ("SYSTEM", "Notification système"),
                        ],
                        max_length=50,
                        verbose_name="type",
                    ),
                ),
                (
                    "channel",
                    models.CharField(
                        choices=[
                            ("PUSH", "Notification push"),
                            ("EMAIL", "Email"),
                            ("SMS", "SMS"),
                            ("IN_APP", "Dans l'application"),
                        ],
                        max_length=20,
                        verbose_name="canal",
                    ),
                ),
                (
                    "subject",
                    models.CharField(blank=True, max_length=255, verbose_name="sujet"),
                ),
                ("content", models.TextField(verbose_name="contenu")),
                ("variables", models.JSONField(default=list, verbose_name="variables")),
                ("is_active", models.BooleanField(default=True, verbose_name="actif")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="créé le"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="mis à jour le"),
                ),
            ],
            options={
                "verbose_name": "modèle de notification",
                "verbose_name_plural": "modèles de notification",
                "unique_together": {("name", "type", "channel")},
            },
        ),
        migrations.CreateModel(
            name="Notification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("TRIP_REQUEST", "Demande de course"),
                            ("TRIP_ACCEPTED", "Course acceptée"),
                            ("TRIP_REJECTED", "Course refusée"),
                            ("TRIP_STARTED", "Course démarrée"),
                            ("TRIP_COMPLETED", "Course terminée"),
                            ("TRIP_CANCELLED", "Course annulée"),
                            ("PAYMENT_RECEIVED", "Paiement reçu"),
                            ("PAYMENT_FAILED", "Paiement échoué"),
                            ("REFUND_PROCESSED", "Remboursement traité"),
                            ("NEW_MESSAGE", "Nouveau message"),
                            ("SYSTEM", "Notification système"),
                        ],
                        max_length=50,
                        verbose_name="type",
                    ),
                ),
                (
                    "channel",
                    models.CharField(
                        choices=[
                            ("PUSH", "Notification push"),
                            ("EMAIL", "Email"),
                            ("SMS", "SMS"),
                            ("IN_APP", "Dans l'application"),
                        ],
                        default="IN_APP",
                        max_length=20,
                        verbose_name="canal",
                    ),
                ),
                ("title", models.CharField(max_length=255, verbose_name="titre")),
                ("message", models.TextField(verbose_name="message")),
                ("object_id", models.PositiveIntegerField(blank=True, null=True)),
                ("is_read", models.BooleanField(default=False, verbose_name="lu")),
                (
                    "read_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="lu le"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="créé le"),
                ),
                (
                    "sent_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="envoyé le"
                    ),
                ),
                (
                    "delivery_status",
                    models.CharField(
                        default="PENDING", max_length=20, verbose_name="statut d'envoi"
                    ),
                ),
                (
                    "error_message",
                    models.TextField(blank=True, verbose_name="message d'erreur"),
                ),
                (
                    "content_type",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notifications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "notification",
                "verbose_name_plural": "notifications",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Device",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "device_id",
                    models.CharField(
                        max_length=255,
                        unique=True,
                        verbose_name="identifiant de l'appareil",
                    ),
                ),
                (
                    "device_type",
                    models.CharField(
                        choices=[
                            ("ANDROID", "Android"),
                            ("IOS", "iOS"),
                            ("WEB", "Web"),
                        ],
                        max_length=20,
                        verbose_name="type d'appareil",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True, max_length=255, verbose_name="nom de l'appareil"
                    ),
                ),
                ("push_token", models.TextField(blank=True, verbose_name="token push")),
                ("is_active", models.BooleanField(default=True, verbose_name="actif")),
                (
                    "last_used",
                    models.DateTimeField(
                        auto_now=True, verbose_name="dernière utilisation"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="créé le"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="devices",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "appareil",
                "verbose_name_plural": "appareils",
                "ordering": ["-last_used"],
            },
        ),
    ]
