"""
Script pour réinitialiser complètement la configuration d'authentification Django
Ce script va:
1. Supprimer toutes les tables problématiques
2. Configurer le projet pour utiliser uniquement rest_framework et SimpleJWT
"""
import os
import sys
import django
from django.db import connection

# Ajouter le répertoire du projet au path
sys.path.append('D:\\commodore')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

def execute_sql(sql):
    """Exécute une requête SQL"""
    with connection.cursor() as cursor:
        cursor.execute(sql)
    print(f"SQL exécuté: {sql}")

def drop_auth_tables():
    """Supprime toutes les tables problématiques"""
    tables_to_drop = [
        'account_emailaddress',
        'account_emailconfirmation',
        'socialaccount_socialaccount',
        'socialaccount_socialapp',
        'socialaccount_socialapp_sites',
        'socialaccount_socialtoken',
        'django_site',
        'authtoken_token'
    ]
    
    print("Suppression des tables problématiques...")
    for table in tables_to_drop:
        try:
            execute_sql(f"DROP TABLE IF EXISTS {table} CASCADE;")
            print(f"✅ Table {table} supprimée")
        except Exception as e:
            print(f"❌ Erreur lors de la suppression de {table}: {e}")

def drop_all_tables():
    """Supprime TOUTES les tables de la base de données"""
    print("Suppression de TOUTES les tables de la base de données...")
    execute_sql("""
    DO $$
    DECLARE
        r RECORD;
    BEGIN
        FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = current_schema()) LOOP
            EXECUTE 'DROP TABLE IF EXISTS ' || quote_ident(r.tablename) || ' CASCADE';
        END LOOP;
    END $$;
    """)
    print("✅ Toutes les tables ont été supprimées")

if __name__ == "__main__":
    # Demander confirmation avant de supprimer toutes les tables
    choice = input("Voulez-vous supprimer TOUTES les tables (T) ou seulement les tables d'authentification (A)? [T/A]: ")
    
    if choice.upper() == 'T':
        drop_all_tables()
    else:
        drop_auth_tables()
    
    print("\nÉtapes suivantes:")
    print("1. Modifiez settings.py pour utiliser uniquement rest_framework et SimpleJWT")
    print("2. Exécutez: python manage.py makemigrations")
    print("3. Exécutez: python manage.py migrate")
    print("\nVotre base de données est maintenant prête pour une configuration propre!")
