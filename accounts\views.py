from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated, AllowAny
from django.contrib.auth import get_user_model
from .models import C<PERSON>, Captain, Establishment
from .serializers import (
    UserSerializer, ClientSerializer, CaptainSerializer,
    EstablishmentSerializer, UserProfileSerializer
)
from django.shortcuts import get_object_or_404

User = get_user_model()

class RegisterUserView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        user_type = request.data.get('type', '').upper()
        if user_type not in ['CLIENT', 'CAPTAIN', 'ESTABLISHMENT']:
            return Response(
                {'error': 'Type d\'utilisateur invalide'},
                status=status.HTTP_400_BAD_REQUEST
            )

        serializer_class = {
            'CLIENT': ClientSerializer,
            'CAPTAIN': CaptainSerializer,
            'ESTABLISHMENT': EstablishmentSerializer
        }.get(user_type)

        serializer = serializer_class(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            return Response(
                serializer.data,
                status=status.HTTP_201_CREATED
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class UserProfileView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        serializer = UserProfileSerializer(request.user)
        return Response(serializer.data)

    def patch(self, request):
        serializer = UserProfileSerializer(
            request.user,
            data=request.data,
            partial=True
        )
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class ClientListView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        clients = Client.objects.all()
        serializer = ClientSerializer(clients, many=True)
        return Response(serializer.data)

class CaptainListView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        captains = Captain.objects.all()
        serializer = CaptainSerializer(captains, many=True)
        return Response(serializer.data)

class EstablishmentListView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        establishments = Establishment.objects.all()
        serializer = EstablishmentSerializer(establishments, many=True)
        return Response(serializer.data)

class UserDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, pk):
        user = get_object_or_404(User, pk=pk)
        serializer = UserProfileSerializer(user)
        return Response(serializer.data)

class CaptainDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, pk):
        captain = get_object_or_404(Captain, pk=pk)
        serializer = CaptainSerializer(captain)
        return Response(serializer.data)

    def patch(self, request, pk):
        captain = get_object_or_404(Captain, pk=pk)
        if captain.user != request.user and not request.user.is_staff:
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        serializer = CaptainSerializer(
            captain,
            data=request.data,
            partial=True
        )
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class EstablishmentDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, pk):
        establishment = get_object_or_404(Establishment, pk=pk)
        serializer = EstablishmentSerializer(establishment)
        return Response(serializer.data)

    def patch(self, request, pk):
        establishment = get_object_or_404(Establishment, pk=pk)
        if establishment.user != request.user and not request.user.is_staff:
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        serializer = EstablishmentSerializer(
            establishment,
            data=request.data,
            partial=True
        )
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
