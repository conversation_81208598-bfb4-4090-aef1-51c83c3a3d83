"""
Module de vues API pour l'application payments.

Ce module contient les vues API pour l'application payments,
permettant la gestion des paiements, des portefeuilles et des transactions.
Ces vues sont basées sur APIView pour une plus grande flexibilité et un contrôle
plus précis des opérations.
"""

import stripe
import json
import logging
import os
from decimal import Decimal
from django.conf import settings
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from .models import Payment, Transaction, Wallet
from .serializers import PaymentSerializer, TransactionSerializer, WalletSerializer
from .stripe_utils import (
    create_payment_intent, create_checkout_session, create_refund,
    handle_webhook_event
)
from trips.models import Trip, Shuttle

# Configuration du logger
logger = logging.getLogger(__name__)

class WalletDetailView(APIView):
    """
    Vue pour consulter les détails d'un portefeuille.

    GET: Récupère les détails d'un portefeuille
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Récupère les détails du portefeuille de l'utilisateur connecté.

        Args:
            request: Requête HTTP

        Returns:
            Response: Données du portefeuille au format JSON
        """
        try:
            wallet = Wallet.objects.get(user=request.user)
            serializer = WalletSerializer(wallet)
            return Response(serializer.data)
        except Wallet.DoesNotExist:
            return Response(
                {"error": "Portefeuille non trouvé pour cet utilisateur."},
                status=status.HTTP_404_NOT_FOUND
            )

class WalletRechargeView(APIView):
    """
    Vue pour recharger un portefeuille.

    POST: Recharge un portefeuille
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Recharge le portefeuille de l'utilisateur connecté.

        Args:
            request: Requête HTTP contenant le montant à recharger

        Returns:
            Response: Données de la session de paiement au format JSON
        """
        amount = request.data.get('amount')
        payment_method_types = request.data.get('payment_method_types', ['card'])
        success_url = request.data.get('success_url')
        cancel_url = request.data.get('cancel_url')
        locale = request.data.get('locale', 'fr')

        if not amount:
            return Response(
                {"error": "Le montant est requis."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Récupérer le portefeuille de l'utilisateur
            wallet = Wallet.objects.get(user=request.user)

            # Convertir le montant en centimes pour Stripe
            amount_cents = int(float(amount) * 100)

            # Créer les métadonnées
            metadata = {
                'wallet_id': str(wallet.id),
                'user_id': str(request.user.id),
                'transaction_type': 'recharge'
            }

            # Créer une session de paiement
            session = create_checkout_session(
                amount=amount_cents,
                currency='eur',
                product_name='Recharge de portefeuille',
                product_description=f'Recharge de {amount}€',
                customer=request.user.stripe_customer_id if hasattr(request.user, 'stripe_customer_id') else None,
                payment_method_types=payment_method_types,
                success_url=success_url,
                cancel_url=cancel_url,
                metadata=metadata,
                locale=locale
            )

            if 'error' in session:
                return Response(
                    {"error": session['error']},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Créer une transaction en attente
            transaction = Transaction.objects.create(
                wallet=wallet,
                amount=Decimal(amount),
                type='CREDIT',
                description='Recharge de portefeuille',
                balance_after=wallet.balance + Decimal(amount),
                metadata={
                    'checkout_session_id': session.id,
                    'stripe_payment_intent_id': session.payment_intent,
                    **metadata
                }
            )

            # Mettre à jour les métadonnées avec l'ID de la transaction
            metadata['transaction_id'] = str(transaction.id)
            stripe.checkout.Session.modify(
                session.id,
                metadata=metadata
            )

            return Response({
                'session_id': session.id,
                'transaction_id': transaction.id,
                'checkout_url': session.url
            })

        except Wallet.DoesNotExist:
            return Response(
                {"error": "Portefeuille non trouvé pour cet utilisateur."},
                status=status.HTTP_404_NOT_FOUND
            )
        except ValueError:
            return Response(
                {"error": "Montant invalide."},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Erreur lors de la recharge du portefeuille: {str(e)}")
            return Response(
                {"error": f"Une erreur est survenue: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class TripPaymentView(APIView):
    """
    Vue pour payer une course ou une navette.

    POST: Paie une course ou une navette
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Paie une course ou une navette avec le portefeuille de l'utilisateur connecté.

        Args:
            request: Requête HTTP contenant l'ID de la course ou de la navette

        Returns:
            Response: Données de la transaction au format JSON
        """
        trip_id = request.data.get('trip_id')
        shuttle_id = request.data.get('shuttle_id')

        if not trip_id and not shuttle_id:
            return Response(
                {"error": "L'ID de la course ou de la navette est requis."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Récupérer la course ou la navette
            if trip_id:
                trip = Trip.objects.get(id=trip_id)
                # Vérifier que l'utilisateur est bien le client de la course
                if not hasattr(request.user, 'client') or trip.client != request.user.client:
                    return Response(
                        {"error": "Vous n'êtes pas autorisé à payer cette course."},
                        status=status.HTTP_403_FORBIDDEN
                    )
                amount = trip.total_price
                payment_type = 'trip'
            else:
                shuttle = Shuttle.objects.get(id=shuttle_id)
                # Pour les navettes, vérifier que l'utilisateur est autorisé
                # TODO: Ajouter la logique de vérification pour les navettes
                amount = shuttle.price_per_person
                payment_type = 'shuttle'

            # Récupérer le portefeuille de l'utilisateur
            wallet = Wallet.objects.get(user=request.user)

            # Vérifier que le solde est suffisant
            if wallet.balance < amount:
                return Response(
                    {"error": "Solde insuffisant. Veuillez recharger votre portefeuille."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Créer la transaction
            transaction = Transaction.objects.create(
                wallet=wallet,
                amount=amount,
                type='DEBIT',
                description=f'Paiement pour {"course" if trip_id else "navette"} ID:{trip_id if trip_id else shuttle_id}',
                balance_after=wallet.balance - amount
            )

            # Créer le paiement
            payment = Payment.objects.create(
                user=request.user,
                wallet=wallet,
                trip=trip if trip_id else None,
                shuttle=shuttle if shuttle_id else None,
                amount=amount,
                type=Payment.PaymentType.TRIP if trip_id else Payment.PaymentType.SHUTTLE,
                payment_method=Payment.PaymentMethod.WALLET,
                status=Payment.Status.PENDING
            )

            # Débiter le portefeuille
            wallet.balance -= amount
            wallet.save()

            # Mettre à jour la transaction avec le paiement
            transaction.payment = payment
            transaction.save()
            payment.status = 'completed'
            payment.save()

            # Mettre à jour le statut
            if trip_id:
                trip.payment_status = 'PAID'
                trip.save()
            else:
                # TODO: Ajouter la logique de mise à jour pour les navettes
                pass

            # Sérialiser la transaction
            serializer = TransactionSerializer(transaction)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except (Trip.DoesNotExist, Shuttle.DoesNotExist):
            return Response(
                {"error": "Course ou navette non trouvée."},
                status=status.HTTP_404_NOT_FOUND
            )
        except Wallet.DoesNotExist:
            return Response(
                {"error": "Portefeuille non trouvé pour cet utilisateur."},
                status=status.HTTP_404_NOT_FOUND
            )
        except ValueError as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Erreur lors du paiement: {str(e)}")
            return Response(
                {"error": f"Une erreur est survenue: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class TipPaymentView(APIView):
    """
    Vue pour ajouter un pourboire.

    POST: Ajoute un pourboire pour une course ou une navette
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Ajoute un pourboire pour une course ou une navette.

        Args:
            request: Requête HTTP contenant l'ID de la course ou de la navette et le montant du pourboire

        Returns:
            Response: Données de la transaction au format JSON
        """
        trip_id = request.data.get('trip_id')
        shuttle_id = request.data.get('shuttle_id')
        amount = request.data.get('amount')

        if (not trip_id and not shuttle_id) or not amount:
            return Response(
                {"error": "L'ID de la course ou de la navette et le montant sont requis."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Convertir le montant en Decimal
            amount = Decimal(amount)

            # Vérifier que le montant est positif
            if amount <= 0:
                return Response(
                    {"error": "Le montant doit être positif."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Récupérer la course ou la navette
            if trip_id:
                trip = Trip.objects.get(id=trip_id)
                # Vérifier que l'utilisateur est bien le client de la course
                if not hasattr(request.user, 'client') or trip.client != request.user.client:
                    return Response(
                        {"error": "Vous n'êtes pas autorisé à ajouter un pourboire pour cette course."},
                        status=status.HTTP_403_FORBIDDEN
                    )
                # Vérifier que la course est terminée
                if trip.status != Trip.Status.COMPLETED:
                    return Response(
                        {"error": "Vous ne pouvez ajouter un pourboire que pour une course terminée."},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            else:
                shuttle = Shuttle.objects.get(id=shuttle_id)
                # Vérifier que la navette est terminée
                if shuttle.status != Shuttle.Status.COMPLETED:
                    return Response(
                        {"error": "Vous ne pouvez ajouter un pourboire que pour une navette terminée."},
                        status=status.HTTP_400_BAD_REQUEST
                    )

            # Récupérer le portefeuille de l'utilisateur
            wallet = Wallet.objects.get(user=request.user)

            # Vérifier que le solde est suffisant
            if wallet.balance < amount:
                return Response(
                    {"error": "Solde insuffisant. Veuillez recharger votre portefeuille."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Créer la transaction
            transaction = Transaction.objects.create(
                wallet=wallet,
                amount=amount,
                type='DEBIT',
                description=f'Pourboire pour {"course" if trip_id else "navette"} ID:{trip_id if trip_id else shuttle_id}',
                balance_after=wallet.balance - amount
            )

            # Créer le paiement
            payment = Payment.objects.create(
                user=request.user,
                wallet=wallet,
                trip=trip if trip_id else None,
                shuttle=shuttle if shuttle_id else None,
                amount=amount,
                type=Payment.PaymentType.TIP,
                payment_method=Payment.PaymentMethod.WALLET,
                status=Payment.Status.PENDING
            )

            # Débiter le portefeuille
            wallet.balance -= amount
            wallet.save()

            # Mettre à jour la transaction avec le paiement
            transaction.payment = payment
            transaction.save()
            payment.status = 'completed'
            payment.save()

            # Sérialiser la transaction
            serializer = TransactionSerializer(transaction)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except (Trip.DoesNotExist, Shuttle.DoesNotExist):
            return Response(
                {"error": "Course ou navette non trouvée."},
                status=status.HTTP_404_NOT_FOUND
            )
        except Wallet.DoesNotExist:
            return Response(
                {"error": "Portefeuille non trouvé pour cet utilisateur."},
                status=status.HTTP_404_NOT_FOUND
            )
        except ValueError:
            return Response(
                {"error": "Montant invalide."},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Erreur lors de l'ajout du pourboire: {str(e)}")
            return Response(
                {"error": f"Une erreur est survenue: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class CarbonOffsetView(APIView):
    """
    Vue pour payer la compensation carbone.

    POST: Paie la compensation carbone pour une course ou une navette
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Paie la compensation carbone pour une course ou une navette.

        Args:
            request: Requête HTTP contenant l'ID de la course ou de la navette et le montant de la compensation

        Returns:
            Response: Données de la transaction au format JSON
        """
        trip_id = request.data.get('trip_id')
        shuttle_id = request.data.get('shuttle_id')
        amount = request.data.get('amount')

        if (not trip_id and not shuttle_id) or not amount:
            return Response(
                {"error": "L'ID de la course ou de la navette et le montant sont requis."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Convertir le montant en Decimal
            amount = Decimal(amount)

            # Vérifier que le montant est positif
            if amount <= 0:
                return Response(
                    {"error": "Le montant doit être positif."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Récupérer la course ou la navette
            if trip_id:
                trip = Trip.objects.get(id=trip_id)
                # Vérifier que l'utilisateur est bien le client de la course
                if not hasattr(request.user, 'client') or trip.client != request.user.client:
                    return Response(
                        {"error": "Vous n'êtes pas autorisé à payer la compensation carbone pour cette course."},
                        status=status.HTTP_403_FORBIDDEN
                    )
                # Vérifier que la course est terminée
                if trip.status != Trip.Status.COMPLETED:
                    return Response(
                        {"error": "Vous ne pouvez payer la compensation carbone que pour une course terminée."},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            else:
                shuttle = Shuttle.objects.get(id=shuttle_id)
                # Vérifier que la navette est terminée
                if shuttle.status != Shuttle.Status.COMPLETED:
                    return Response(
                        {"error": "Vous ne pouvez payer la compensation carbone que pour une navette terminée."},
                        status=status.HTTP_400_BAD_REQUEST
                    )

            # Récupérer le portefeuille de l'utilisateur
            wallet = Wallet.objects.get(user=request.user)

            # Vérifier que le solde est suffisant
            if wallet.balance < amount:
                return Response(
                    {"error": "Solde insuffisant. Veuillez recharger votre portefeuille."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Créer la transaction
            transaction = Transaction.objects.create(
                wallet=wallet,
                amount=amount,
                type='CARBON_OFFSET',
                status='pending'
            )

            # Créer le paiement
            payment = Payment.objects.create(
                user=request.user,
                wallet=wallet,
                trip=trip if trip_id else None,
                shuttle=shuttle if shuttle_id else None,
                amount=amount,
                type='CARBON_OFFSET',
                status='pending'
            )

            # Débiter le portefeuille
            wallet.balance -= amount
            wallet.save()

            # Compléter la transaction et le paiement
            transaction.status = 'completed'
            transaction.save()
            payment.status = 'completed'
            payment.save()

            # Mettre à jour la compensation carbone de la course ou de la navette
            if trip_id:
                trip.carbon_compensation = amount
                trip.save()
            else:
                shuttle.carbon_compensation = amount
                shuttle.save()

            # Sérialiser la transaction
            serializer = TransactionSerializer(transaction)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except (Trip.DoesNotExist, Shuttle.DoesNotExist):
            return Response(
                {"error": "Course ou navette non trouvée."},
                status=status.HTTP_404_NOT_FOUND
            )
        except Wallet.DoesNotExist:
            return Response(
                {"error": "Portefeuille non trouvé pour cet utilisateur."},
                status=status.HTTP_404_NOT_FOUND
            )
        except ValueError:
            return Response(
                {"error": "Montant invalide."},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Erreur lors du paiement de la compensation carbone: {str(e)}")
            return Response(
                {"error": f"Une erreur est survenue: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class RefundPaymentView(APIView):
    """
    Vue pour rembourser un paiement.

    POST: Rembourse un paiement
    """
    permission_classes = [IsAuthenticated]

    def post(self, request, id=None):
        """
        Rembourse un paiement pour une transaction.

        Args:
            request: Requête HTTP contenant les détails du remboursement
            id: ID de la transaction à rembourser (depuis l'URL)

        Returns:
            Response: Données de la transaction de remboursement au format JSON
        """
        transaction_id = id or request.data.get('transaction_id')
        reason = request.data.get('reason', 'Remboursement demandé par le client')

        if not transaction_id:
            return Response(
                {"error": "L'ID de la transaction est requis."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Récupérer la transaction
            payment_transaction = Transaction.objects.get(id=transaction_id)

            # Récupérer le paiement associé
            payment = payment_transaction.payment
            if not payment:
                return Response(
                    {"error": "Aucun paiement associé à cette transaction."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Vérifier que l'utilisateur est bien le propriétaire du portefeuille ou un administrateur
            if payment_transaction.wallet.user != request.user and not request.user.is_staff:
                return Response(
                    {"error": "Vous n'êtes pas autorisé à rembourser cette transaction."},
                    status=status.HTTP_403_FORBIDDEN
                )

            # Vérifier si un remboursement existe déjà
            existing_refund = Transaction.objects.filter(
                payment=payment,
                type=Transaction.TransactionType.REFUND
            ).exists()

            if existing_refund:
                return Response(
                    {"error": "Ce paiement a déjà été remboursé."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Créer la transaction de remboursement
            refund_transaction = Transaction.objects.create(
                wallet=payment_transaction.wallet,
                payment=payment,
                type=Transaction.TransactionType.REFUND,
                amount=payment_transaction.amount,
                balance_after=payment_transaction.wallet.balance + payment_transaction.amount,
                description=f"Remboursement: {reason}",
                metadata={
                    'original_transaction_id': str(payment_transaction.id),
                    'reason': reason
                }
            )

            # Rembourser le portefeuille
            wallet = payment_transaction.wallet
            wallet.balance += payment_transaction.amount
            wallet.save()

            # Mettre à jour le statut du trip ou de la navette si présent
            if payment.trip:
                payment.trip.payment_status = 'REFUNDED'
                payment.trip.save()
            elif payment.shuttle:
                # Si le projet a un statut pour les navettes
                if hasattr(payment.shuttle, 'payment_status'):
                    payment.shuttle.payment_status = 'REFUNDED'
                    payment.shuttle.save()

            # Mettre à jour le statut du paiement
            payment.status = Payment.Status.REFUNDED
            payment.save()

            # Sérialiser la transaction
            serializer = TransactionSerializer(refund_transaction)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except Transaction.DoesNotExist:
            return Response(
                {"error": "Transaction non trouvée."},
                status=status.HTTP_404_NOT_FOUND
            )
        except Payment.DoesNotExist:
            return Response(
                {"error": "Paiement non trouvé."},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Erreur lors du remboursement: {str(e)}")
            return Response(
                {"error": f"Une erreur est survenue: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class StripeWebhookView(APIView):
    """
    Vue pour gérer les webhooks Stripe.

    POST: Traite un événement webhook Stripe
    """
    permission_classes = [AllowAny]  # Pas d'authentification pour les webhooks Stripe

    @method_decorator(csrf_exempt)
    def dispatch(self, *args, **kwargs):
        return super().dispatch(*args, **kwargs)

    def post(self, request):
        """
        Traite un événement webhook Stripe.

        Args:
            request: Requête HTTP contenant l'événement Stripe

        Returns:
            HttpResponse: Réponse HTTP 200 OK
        """
        try:
            payload = request.body
            logger.info(f"Webhook payload reçu: {payload.decode('utf-8')[:200]}...")
            
            sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
            logger.info(f"En-tête de signature: {sig_header}")
            # Ne pas logger la clé secrète complète pour des raisons de sécurité
            if settings.STRIPE_WEBHOOK_SECRET:
                logger.info("Clé secrète webhook: configurée")
            else:
                logger.error("Clé secrète webhook: non configurée")
            
            # Log all request headers for debugging
            logger.info("Headers de la requête:")
            for key, value in request.META.items():
                if key.startswith('HTTP_'):
                    logger.info(f"{key}: {value}")

            if not sig_header:
                logger.error("Pas d'en-tête de signature Stripe")
                return Response(
                    {"error": "Pas d'en-tête de signature Stripe"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Log du type de payload et de son contenu
            logger.info(f"Type de payload: {type(payload)}")
            try:
                payload_str = payload.decode('utf-8')
                logger.info(f"Contenu du payload: {payload_str[:200]}...")
            except Exception as e:
                logger.error(f"Erreur lors du décodage du payload: {str(e)}")

            # Log de l'en-tête de signature et de la clé webhook
            logger.info(f"En-tête de signature: {sig_header}")
            logger.info(f"Clé webhook configurée: {bool(settings.STRIPE_WEBHOOK_SECRET)}")
            logger.info(f"Longueur de la clé webhook: {len(settings.STRIPE_WEBHOOK_SECRET) if settings.STRIPE_WEBHOOK_SECRET else 0}")
            # Log des premiers et derniers caractères de la clé webhook pour débogage
            if settings.STRIPE_WEBHOOK_SECRET:
                webhook_key = settings.STRIPE_WEBHOOK_SECRET
                logger.info(f"Début de la clé webhook: {webhook_key[:4]}...{webhook_key[-4:]}")
                logger.info(f"Clé webhook complète pour debug: {webhook_key}")
            
            try:
                # Utilisation directe de la clé webhook depuis les variables d'environnement
                webhook_secret = os.environ.get('STRIPE_WEBHOOK_SECRET')
                logger.info(f"Clé webhook depuis env: {webhook_secret[:4]}...{webhook_secret[-4:]}")
                
                event = stripe.Webhook.construct_event(
                    payload, sig_header, webhook_secret
                )
            except ValueError as e:
                logger.error(f"Erreur de payload: {str(e)}")
                return Response(
                    {"error": "Payload invalide"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            except stripe.error.SignatureVerificationError as e:
                logger.error(f"Erreur de vérification de signature: {str(e)}")
                return Response(
                    {"error": "Signature invalide"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Vérifier que le payload est bien en bytes
            if not isinstance(payload, bytes):
                logger.error("Le payload n'est pas au format bytes")
                return Response(
                    {"error": "Format de payload invalide"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            event = handle_webhook_event(payload, sig_header)

            if 'error' in event:
                logger.error(f"Erreur de webhook: {event['error']}")
                return Response(
                    {"error": event['error']},
                    status=status.HTTP_400_BAD_REQUEST
                )

            logger.info(f"Événement webhook reçu: {event.type}")

            # Gérer l'événement
            if event.type == 'payment_intent.succeeded':
                self._handle_payment_intent_succeeded(event.data.object)
            elif event.type == 'payment_intent.payment_failed':
                self._handle_payment_intent_failed(event.data.object)
            elif event.type == 'checkout.session.completed':
                self._handle_checkout_session_completed(event.data.object)
            elif event.type == 'checkout.session.expired':
                self._handle_checkout_session_expired(event.data.object)
            elif event.type == 'charge.refunded':
                self._handle_charge_refunded(event.data.object)

            # Retourner une réponse pour accuser réception de l'événement
            return HttpResponse(status=200)

        except Exception as e:
            logger.error(f"Erreur lors du traitement du webhook: {str(e)}")
            # Nous retournons quand même un 200 pour que Stripe ne réessaie pas
            return HttpResponse(status=200)

    def _handle_payment_intent_succeeded(self, payment_intent):
        """
        Gère l'événement payment_intent.succeeded.

        Args:
            payment_intent: Objet payment_intent de Stripe
        """
        # Récupérer la transaction à partir des métadonnées
        transaction_id = payment_intent.metadata.get('transaction_id')
        wallet_id = payment_intent.metadata.get('wallet_id')

        if transaction_id:
            try:
                transaction = Transaction.objects.get(id=transaction_id)
                transaction.status = 'completed'
                transaction.stripe_payment_intent_id = payment_intent.id
                transaction.save()
                logger.info(f"Transaction {transaction_id} marquée comme completed")
            except Transaction.DoesNotExist:
                logger.error(f"Transaction {transaction_id} non trouvée")
        elif wallet_id:
            # C'est une recharge de portefeuille
            try:
                wallet = Wallet.objects.get(id=wallet_id)
                amount = Decimal(payment_intent.amount) / 100  # Convertir les centimes en euros

                # Créer une transaction si elle n'existe pas déjà
                transaction, created = Transaction.objects.get_or_create(
                    wallet=wallet,
                    stripe_payment_intent_id=payment_intent.id,
                    defaults={
                        'amount': amount,
                        'type': 'recharge',
                        'status': 'completed'
                    }
                )

                if not created:
                    transaction.status = 'completed'
                    transaction.save()

                # Recharger le portefeuille
                wallet.balance += amount
                wallet.save()
                logger.info(f"Portefeuille {wallet_id} rechargé de {amount}€")
            except Wallet.DoesNotExist:
                logger.error(f"Portefeuille {wallet_id} non trouvé")
            except Exception as e:
                logger.error(f"Erreur lors de la recharge du portefeuille: {str(e)}")

    def _handle_payment_intent_failed(self, payment_intent):
        """
        Gère l'événement payment_intent.payment_failed.

        Args:
            payment_intent: Objet payment_intent de Stripe
        """
        transaction_id = payment_intent.metadata.get('transaction_id')

        if transaction_id:
            try:
                transaction = Transaction.objects.get(id=transaction_id)
                transaction.status = 'failed'
                transaction.save()
                logger.info(f"Transaction {transaction_id} marquée comme failed")
            except Transaction.DoesNotExist:
                logger.error(f"Transaction {transaction_id} non trouvée")

    def _handle_checkout_session_completed(self, session):
        """
        Gère l'événement checkout.session.completed.

        Args:
            session: Objet checkout.session de Stripe
        """
        wallet_id = session.metadata.get('wallet_id')
        transaction_type = session.metadata.get('transaction_type')

        if wallet_id and transaction_type == 'recharge':
            try:
                wallet = Wallet.objects.get(id=wallet_id)
                amount = Decimal(session.amount_total) / 100  # Convertir les centimes en euros

                # Créer une transaction
                transaction = Transaction.objects.create(
                    wallet=wallet,
                    amount=amount,
                    type='recharge',
                    status='completed',
                    stripe_payment_intent_id=session.payment_intent,
                    metadata={
                        'checkout_session_id': session.id
                    }
                )

                # Recharger le portefeuille
                wallet.balance += amount
                wallet.save()
                logger.info(f"Portefeuille {wallet_id} rechargé de {amount}€ via checkout")
            except Wallet.DoesNotExist:
                logger.error(f"Portefeuille {wallet_id} non trouvé")
            except Exception as e:
                logger.error(f"Erreur lors de la recharge du portefeuille: {str(e)}")

    def _handle_checkout_session_expired(self, session):
        """
        Gère l'événement checkout.session.expired.

        Args:
            session: Objet checkout.session de Stripe
        """
        transaction_id = session.metadata.get('transaction_id')

        if transaction_id:
            try:
                transaction = Transaction.objects.get(id=transaction_id)
                transaction.status = 'failed'
                transaction.save()
                logger.info(f"Transaction {transaction_id} marquée comme failed (session expirée)")
            except Transaction.DoesNotExist:
                logger.error(f"Transaction {transaction_id} non trouvée")

    def _handle_charge_refunded(self, charge):
        """
        Gère l'événement charge.refunded.

        Args:
            charge: Objet charge de Stripe
        """
        payment_intent_id = charge.payment_intent

        if payment_intent_id:
            try:
                # Trouver la transaction de paiement
                payment_transaction = Transaction.objects.filter(
                    stripe_payment_intent_id=payment_intent_id,
                    type='payment'
                ).first()

                if payment_transaction:
                    # Vérifier si un remboursement existe déjà
                    refund_transaction = Transaction.objects.filter(
                        ride=payment_transaction.ride,
                        type='refund'
                    ).first()

                    if not refund_transaction:
                        # Créer une transaction de remboursement
                        refund_transaction = Transaction.objects.create(
                            wallet=payment_transaction.wallet,
                            ride=payment_transaction.ride,
                            amount=payment_transaction.amount,
                            type='refund',
                            status='completed',
                            metadata={
                                'payment_transaction_id': str(payment_transaction.id),
                                'charge_id': charge.id
                            }
                        )

                        # Rembourser le portefeuille
                        payment_transaction.wallet.balance += payment_transaction.amount
                        payment_transaction.wallet.save()

                        # Mettre à jour le statut du trajet
                        if payment_transaction.ride and payment_transaction.ride.status != 'canceled':
                            payment_transaction.ride.status = 'canceled'
                            payment_transaction.ride.save()

                        logger.info(f"Remboursement créé pour la transaction {payment_transaction.id}")
                    else:
                        logger.info(f"Remboursement déjà existant pour la transaction {payment_transaction.id}")
                else:
                    logger.error(f"Aucune transaction de paiement trouvée pour le payment_intent {payment_intent_id}")
            except Exception as e:
                logger.error(f"Erreur lors du traitement du remboursement: {str(e)}")