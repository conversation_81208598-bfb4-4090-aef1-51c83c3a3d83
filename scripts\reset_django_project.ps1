# Script PowerShell pour réinitialiser complètement un projet Django
# Ce script va:
# 1. Supprimer la base de données
# 2. Recréer la base de données
# 3. Supprimer tous les fichiers de migration (sauf __init__.py)
# 4. Supprimer tous les dossiers __pycache__
# 5. Recréer les migrations et les appliquer

# Fonction pour supprimer les migrations
function Remove-Migrations {
    param (
        [string]$rootDir
    )
    
    Write-Host "Suppression des fichiers de migration..." -ForegroundColor Yellow
    
    # Trouver tous les dossiers de migrations
    $migrationDirs = Get-ChildItem -Path $rootDir -Recurse -Directory | Where-Object { $_.Name -eq "migrations" }
    
    foreach ($dir in $migrationDirs) {
        Write-Host "Traitement du dossier: $($dir.FullName)" -ForegroundColor Cyan
        
        # Supprimer tous les fichiers sauf __init__.py
        Get-ChildItem -Path $dir.FullName -File | Where-Object { $_.Name -ne "__init__.py" } | ForEach-Object {
            Write-Host "  Suppression de $($_.Name)" -ForegroundColor Gray
            Remove-Item $_.FullName -Force
        }
    }
    
    Write-Host "Suppression des fichiers de migration terminée." -ForegroundColor Green
}

# Fonction pour supprimer les dossiers __pycache__
function Remove-PycacheDirectories {
    param (
        [string]$rootDir
    )
    
    Write-Host "Suppression des dossiers __pycache__..." -ForegroundColor Yellow
    
    # Trouver tous les dossiers __pycache__
    $pycacheDirs = Get-ChildItem -Path $rootDir -Recurse -Directory | Where-Object { $_.Name -eq "__pycache__" }
    
    foreach ($dir in $pycacheDirs) {
        Write-Host "  Suppression de $($dir.FullName)" -ForegroundColor Gray
        Remove-Item $dir.FullName -Recurse -Force
    }
    
    Write-Host "Suppression des dossiers __pycache__ terminée." -ForegroundColor Green
}

# Réinitialiser la base de données
Write-Host "Réinitialisation de la base de données..." -ForegroundColor Yellow

# Supprimer la base de données
$dropDbCommand = "DROP DATABASE IF EXISTS commodore_db;"
$createDbCommand = "CREATE DATABASE commodore_db;"

# Exécuter les commandes SQL
Write-Host "Suppression de la base de données..." -ForegroundColor Cyan
$env:PGPASSWORD = "admin"  # Mot de passe PostgreSQL
psql -U postgres -c "$dropDbCommand"

Write-Host "Création de la base de données..." -ForegroundColor Cyan
psql -U postgres -c "$createDbCommand"

# Supprimer les migrations et les caches
$projectRoot = "D:\commodore"
Remove-Migrations -rootDir $projectRoot
Remove-PycacheDirectories -rootDir $projectRoot



pip install django==4.2.8 --force-reinstall
# Recréer les migrations et les appliquer
Write-Host "Création des nouvelles migrations..." -ForegroundColor Yellow
python manage.py makemigrations


Write-Host "Application des migrations..." -ForegroundColor Yellow
python manage.py migrate

Write-Host "Réinitialisation du projet Django terminée !" -ForegroundColor Green
