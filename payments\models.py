from django.db import models
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator
from accounts.models import User
from trips.models import Trip, Shuttle
from decimal import Decimal

class Payment(models.Model):
    """Modèle de paiement pour toutes les transactions financières"""
    
    class PaymentType(models.TextChoices):
        TRIP = 'TRIP', _('Course')
        SHUTTLE = 'SHUTTLE', _('Navette')
        TIP = 'TIP', _('Pourboire')
        CARBON_OFFSET = 'CARBON_OFFSET', _('Compensation carbone')
        WALLET_RECHARGE = 'WALLET_RECHARGE', _('Recharge portefeuille')
        SUBSCRIPTION = 'SUBSCRIPTION', _('Abonnement')
        ESTABLISHMENT = 'ESTABLISHMENT', _('Établissement')
        MAINTENANCE = 'MAINTENANCE', _('Maintenance')
        PROMOTION = 'PROMOTION', _('Promotion')
        SHARED_TRIP = 'SHARED_TRIP', _('Course partagée')

    class Status(models.TextChoices):
        PENDING = 'PENDING', _('En attente')
        PROCESSING = 'PROCESSING', _('En cours')
        COMPLETED = 'COMPLETED', _('Complété')
        FAILED = 'FAILED', _('Échoué')
        REFUNDED = 'REFUNDED', _('Remboursé')
        PARTIALLY_REFUNDED = 'PARTIALLY_REFUNDED', _('Partiellement remboursé')
        CANCELED = 'CANCELED', _('Annulé')

    class PaymentMethod(models.TextChoices):
        CARD = 'CARD', _('Carte bancaire')
        WALLET = 'WALLET', _('Portefeuille')
        APPLE_PAY = 'APPLE_PAY', _('Apple Pay')
        GOOGLE_PAY = 'GOOGLE_PAY', _('Google Pay')
        SEPA = 'SEPA', _('Prélèvement SEPA')
        TRANSFER = 'TRANSFER', _('Virement bancaire')

    # Relations
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='payments',
                          verbose_name=_('Utilisateur'))
    wallet = models.ForeignKey('Wallet', on_delete=models.SET_NULL, null=True, blank=True,
                            related_name='payments', verbose_name=_('Portefeuille'))
    trip = models.ForeignKey(Trip, on_delete=models.SET_NULL, null=True, blank=True,
                          related_name='payments', verbose_name=_('Course'))
    shuttle = models.ForeignKey(Shuttle, on_delete=models.SET_NULL, null=True, blank=True,
                             related_name='payments', verbose_name=_('Navette'))

    # Informations de base
    amount = models.DecimalField(_('Montant'), max_digits=10, decimal_places=2,
                              validators=[MinValueValidator(Decimal('0.00'))])
    currency = models.CharField(_('Devise'), max_length=3, default='EUR')
    type = models.CharField(_('Type'), max_length=20, choices=PaymentType.choices)
    payment_method = models.CharField(_('Méthode de paiement'), max_length=20,
                                   choices=PaymentMethod.choices)
    status = models.CharField(_('Statut'), max_length=20, choices=Status.choices,
                           default=Status.PENDING)
    description = models.TextField(_('Description'), blank=True)

    # Informations Stripe
    stripe_payment_id = models.CharField(_('ID Stripe'), max_length=255, null=True, blank=True)
    stripe_payment_intent_id = models.CharField(_('ID intention de paiement'), max_length=255,
                                             null=True, blank=True)
    stripe_customer_id = models.CharField(_('ID client Stripe'), max_length=255,
                                       null=True, blank=True)
    stripe_refund_id = models.CharField(_('ID remboursement Stripe'), max_length=255,
                                     null=True, blank=True)
    receipt_url = models.URLField(_('URL du reçu'), max_length=500, null=True, blank=True)

    # Informations de remboursement
    refund_amount = models.DecimalField(_('Montant remboursé'), max_digits=10, decimal_places=2,
                                     null=True, blank=True)
    refund_reason = models.CharField(_('Raison du remboursement'), max_length=100,
                                  null=True, blank=True)
    refunded_at = models.DateTimeField(_('Date de remboursement'), null=True, blank=True)

    # Points de fidélité
    loyalty_points_earned = models.PositiveIntegerField(_('Points de fidélité gagnés'),
                                                     default=0)

    # Métadonnées
    metadata = models.JSONField(_('Métadonnées'), default=dict, blank=True)
    created_at = models.DateTimeField(_('Créé le'), default=timezone.now)
    updated_at = models.DateTimeField(_('Mis à jour le'), auto_now=True)
    completed_at = models.DateTimeField(_('Complété le'), null=True, blank=True)

    class Meta:
        verbose_name = _('Paiement')
        verbose_name_plural = _('Paiements')
        ordering = ['-created_at']

    def __str__(self):
        payment_type = self.get_type_display()
        if self.trip:
            return f"Paiement {self.id} - {self.amount}€ - Course {self.trip.id}"
        elif self.shuttle:
            return f"Paiement {self.id} - {self.amount}€ - Navette {self.shuttle.id}"
        return f"Paiement {self.id} - {self.amount}€ - {payment_type}"

    def calculate_loyalty_points(self):
        """Calcule les points de fidélité pour ce paiement"""
        if self.type in [self.PaymentType.TRIP, self.PaymentType.SHUTTLE]:
            # 1 point par euro dépensé
            return int(self.amount)
        return 0

    def save(self, *args, **kwargs):
        # Calculer les points de fidélité si le paiement est complété
        if self.status == self.Status.COMPLETED and not self.completed_at:
            self.completed_at = timezone.now()
            self.loyalty_points_earned = self.calculate_loyalty_points()

        # Mettre à jour la date de remboursement
        if self.status in [self.Status.REFUNDED, self.Status.PARTIALLY_REFUNDED] and not self.refunded_at:
            self.refunded_at = timezone.now()

        super().save(*args, **kwargs)

class Wallet(models.Model):
    """
    Modèle de portefeuille électronique pour les clients et établissements.
    Permet de stocker des fonds et gérer les points de fidélité.
    """

    class WalletType(models.TextChoices):
        CLIENT = 'CLIENT', _('Client')
        ESTABLISHMENT = 'ESTABLISHMENT', _('Établissement')
        CAPTAIN = 'CAPTAIN', _('Capitaine')

    # Relations et informations de base
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='wallet',
                              verbose_name=_('Utilisateur'))
    type = models.CharField(_('Type'), max_length=20, choices=WalletType.choices)
    currency = models.CharField(_('Devise'), max_length=3, default='EUR')

    # Soldes et points
    balance = models.DecimalField(_('Solde'), max_digits=10, decimal_places=2,
                                validators=[MinValueValidator(Decimal('0.00'))],
                                default=Decimal('0.00'))
    loyalty_points = models.PositiveIntegerField(_('Points de fidélité'), default=0)
    total_earned = models.DecimalField(_('Total gagné'), max_digits=10, decimal_places=2,
                                     default=Decimal('0.00'))
    total_spent = models.DecimalField(_('Total dépensé'), max_digits=10, decimal_places=2,
                                    default=Decimal('0.00'))

    # Informations Stripe
    stripe_customer_id = models.CharField(_('ID client Stripe'), max_length=255,
                                       null=True, blank=True)
    default_payment_method = models.CharField(_('Méthode de paiement par défaut'),
                                           max_length=255, null=True, blank=True)

    # Paramètres et limites
    daily_limit = models.DecimalField(_('Limite quotidienne'), max_digits=10,
                                    decimal_places=2, null=True, blank=True)
    transaction_limit = models.DecimalField(_('Limite par transaction'), max_digits=10,
                                          decimal_places=2, null=True, blank=True)

    # Métadonnées
    metadata = models.JSONField(_('Métadonnées'), default=dict, blank=True)
    created_at = models.DateTimeField(_('Créé le'), default=timezone.now)
    updated_at = models.DateTimeField(_('Mis à jour le'), auto_now=True)
    last_transaction_at = models.DateTimeField(_('Dernière transaction le'),
                                             null=True, blank=True)

    class Meta:
        verbose_name = _('Portefeuille')
        verbose_name_plural = _('Portefeuilles')
        ordering = ['-created_at']

    def __str__(self):
        return f"Portefeuille {self.get_type_display()} - {self.user.get_full_name()} - {self.balance}€"

    def add_funds(self, amount, payment_method=None, description=None):
        """
        Ajoute des fonds au portefeuille
        """
        if amount <= 0:
            raise ValueError(_('Le montant doit être positif'))

        self.balance += amount
        self.total_earned += amount
        self.last_transaction_at = timezone.now()
        self.save()

        # Créer un paiement
        return Payment.objects.create(
            user=self.user,
            wallet=self,
            amount=amount,
            type=Payment.PaymentType.WALLET_RECHARGE,
            payment_method=payment_method or Payment.PaymentMethod.CARD,
            status=Payment.Status.COMPLETED,
            description=description or _('Recharge du portefeuille'),
            completed_at=timezone.now()
        )

    def withdraw(self, amount, payment_type, trip=None, shuttle=None, description=None):
        """
        Retire des fonds du portefeuille
        """
        if amount <= 0:
            raise ValueError(_('Le montant doit être positif'))

        if self.balance < amount:
            raise ValueError(_('Solde insuffisant'))

        if self.daily_limit and self._get_daily_spending() + amount > self.daily_limit:
            raise ValueError(_('Limite quotidienne atteinte'))

        if self.transaction_limit and amount > self.transaction_limit:
            raise ValueError(_('Limite par transaction dépassée'))

        self.balance -= amount
        self.total_spent += amount
        self.last_transaction_at = timezone.now()
        self.save()

        # Créer un paiement
        payment = Payment.objects.create(
            user=self.user,
            wallet=self,
            trip=trip,
            shuttle=shuttle,
            amount=amount,
            type=payment_type,
            payment_method=Payment.PaymentMethod.WALLET,
            status=Payment.Status.COMPLETED,
            description=description,
            completed_at=timezone.now()
        )

        # Ajouter les points de fidélité
        points_earned = payment.calculate_loyalty_points()
        if points_earned > 0:
            self.loyalty_points += points_earned
            self.save()

        return payment

    def _get_daily_spending(self):
        """
        Calcule le total des dépenses du jour
        """
        today = timezone.now().date()
        return Payment.objects.filter(
            wallet=self,
            created_at__date=today,
            status=Payment.Status.COMPLETED
        ).aggregate(total=models.Sum('amount'))['total'] or Decimal('0.00')

    def can_pay(self, amount):
        """
        Vérifie si le portefeuille peut payer un montant donné
        """
        if amount <= 0:
            return False

        if self.balance < amount:
            return False

        if self.daily_limit and self._get_daily_spending() + amount > self.daily_limit:
            return False

        if self.transaction_limit and amount > self.transaction_limit:
            return False

        return True


class Transaction(models.Model):
    """
    Modèle de transaction pour suivre tous les mouvements de fonds dans les portefeuilles.
    Chaque transaction est liée à un paiement pour assurer la traçabilité.
    """

    class TransactionType(models.TextChoices):
        CREDIT = 'CREDIT', _('Crédit')
        DEBIT = 'DEBIT', _('Débit')
        REFUND = 'REFUND', _('Remboursement')
        TRANSFER = 'TRANSFER', _('Transfert')
        ADJUSTMENT = 'ADJUSTMENT', _('Ajustement')

    # Relations
    wallet = models.ForeignKey(Wallet, on_delete=models.CASCADE,
                              related_name='transactions',
                              verbose_name=_('Portefeuille'))
    payment = models.ForeignKey(Payment, on_delete=models.SET_NULL,
                              null=True, blank=True, related_name='transactions',
                              verbose_name=_('Paiement'))
    related_transaction = models.ForeignKey('self', on_delete=models.SET_NULL,
                                          null=True, blank=True,
                                          related_name='linked_transactions',
                                          verbose_name=_('Transaction liée'))

    # Informations de base
    type = models.CharField(_('Type'), max_length=20, choices=TransactionType.choices)
    amount = models.DecimalField(_('Montant'), max_digits=10, decimal_places=2)
    balance_after = models.DecimalField(_('Solde après'), max_digits=10, decimal_places=2)
    description = models.TextField(_('Description'), blank=True)

    # Métadonnées
    metadata = models.JSONField(_('Métadonnées'), default=dict, blank=True)
    created_at = models.DateTimeField(_('Créé le'), default=timezone.now)

    class Meta:
        verbose_name = _('Transaction')
        verbose_name_plural = _('Transactions')
        ordering = ['-created_at']

    def __str__(self):
        return f"Transaction {self.id} - {self.get_type_display()} - {self.amount}€ - {self.wallet}"

    def save(self, *args, **kwargs):
        # Mettre à jour le solde après la transaction
        if not self.balance_after:
            self.balance_after = self.wallet.balance

        # Générer une description par défaut si non fournie
        if not self.description:
            if self.type == self.TransactionType.CREDIT:
                self.description = _('Crédit du portefeuille')
            elif self.type == self.TransactionType.DEBIT:
                self.description = _('Débit du portefeuille')
            elif self.type == self.TransactionType.REFUND:
                self.description = _('Remboursement')
            elif self.type == self.TransactionType.TRANSFER:
                self.description = _('Transfert de fonds')
            elif self.type == self.TransactionType.ADJUSTMENT:
                self.description = _('Ajustement du solde')

        super().save(*args, **kwargs)


@receiver(post_save, sender=User)
def create_wallet(sender, instance, created, **kwargs):
    """
    Crée automatiquement un portefeuille pour chaque nouvel utilisateur.
    Le type de portefeuille est déterminé en fonction du type d'utilisateur.
    """
    if created:
        # Déterminer le type de portefeuille en fonction du type d'utilisateur
        if hasattr(instance, 'is_captain') and instance.is_captain:
            wallet_type = Wallet.WalletType.CAPTAIN
        elif hasattr(instance, 'is_establishment') and instance.is_establishment:
            wallet_type = Wallet.WalletType.ESTABLISHMENT
        else:
            wallet_type = Wallet.WalletType.CLIENT

        # Créer le portefeuille avec le type approprié
        Wallet.objects.create(
            user=instance,
            type=wallet_type,
            currency='EUR'
        )
