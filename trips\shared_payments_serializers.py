from rest_framework import serializers
from .shared_payments import SharedPaymentInvitation, ShuttleBooking
from .models import Trip, Shuttle
from accounts.serializers import UserSerializer, ClientSerializer

class SharedPaymentInvitationSerializer(serializers.ModelSerializer):
    inviter_details = UserSerializer(source='inviter', read_only=True)
    invitee_details = UserSerializer(source='invitee', read_only=True)
    
    class Meta:
        model = SharedPaymentInvitation
        fields = [
            'id', 'trip', 'inviter', 'inviter_details', 'invitee_email', 
            'invitee', 'invitee_details', 'amount', 'token', 'status', 
            'message', 'created_at', 'updated_at', 'expires_at'
        ]
        read_only_fields = ['id', 'token', 'created_at', 'updated_at']
        extra_kwargs = {
            'inviter': {'write_only': True},
            'invitee': {'write_only': True},
        }
    
    def validate(self, data):
        """Validation personnalisée pour s'assurer que le montant est cohérent."""
        trip = data.get('trip')
        amount = data.get('amount')
        
        if trip and amount:
            # Vérifier que le montant n'est pas supérieur au prix total du trajet
            if amount > trip.total_price:
                raise serializers.ValidationError("Le montant ne peut pas être supérieur au prix total du trajet.")
                
            # Vérifier que les invitations existantes plus ce montant ne dépassent pas le total
            existing_invitations = SharedPaymentInvitation.objects.filter(
                trip=trip, 
                status__in=['PENDING', 'ACCEPTED', 'PAID']
            )
            existing_total = sum(inv.amount for inv in existing_invitations)
            
            if existing_total + amount > trip.total_price:
                raise serializers.ValidationError(
                    "Le montant total des invitations de paiement partagé ne peut pas dépasser le prix du trajet."
                )
        
        return data

class ShuttleBookingSerializer(serializers.ModelSerializer):
    client_details = ClientSerializer(source='client', read_only=True)
    total_price = serializers.SerializerMethodField()
    
    class Meta:
        model = ShuttleBooking
        fields = [
            'id', 'shuttle', 'client', 'client_details', 'booking_reference', 
            'number_of_seats', 'seat_numbers', 'passenger_names', 'special_requests', 
            'status', 'amount_paid', 'transaction_id', 'payment_method', 
            'created_at', 'updated_at', 'total_price'
        ]
        read_only_fields = ['id', 'booking_reference', 'created_at', 'updated_at', 'total_price']
        extra_kwargs = {
            'client': {'write_only': True},
        }
    
    def get_total_price(self, obj):
        """Calcule le prix total de la réservation."""
        return obj.calculate_total_price()
    
    def validate(self, data):
        """Validation personnalisée pour s'assurer que les places sont disponibles."""
        shuttle = data.get('shuttle')
        number_of_seats = data.get('number_of_seats', 1)
        
        if shuttle and number_of_seats:
            # Vérifier la disponibilité des places
            available_seats = shuttle.max_capacity - shuttle.current_bookings
            if number_of_seats > available_seats:
                raise serializers.ValidationError(
                    f"Il n'y a que {available_seats} places disponibles sur cette navette."
                )
                
            # Vérifier que la navette n'est pas annulée ou terminée
            if shuttle.status in ['COMPLETED', 'CANCELLED']:
                raise serializers.ValidationError("Cette navette n'est plus disponible à la réservation.")
        
        return data
