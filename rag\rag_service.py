import os
import time
import hashlib
import logging
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from django.conf import settings
from django.utils import timezone
from django.core.cache import cache, caches
import json
import retrying
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_community.llms import DeepInfra
from langchain.prompts import ChatPromptTemplate
from langchain.schema import Document as LangchainDocument
from langchain.schema.runnable import RunnablePassthrough
from langchain.schema.output_parser import StrOutputParser
from langchain_community.vectorstores import FAISS
from .models import Document, DocumentChunk, ChatSession, ChatMessage
import retrying

# Configuration du logger pour le suivi des événements et erreurs
logger = logging.getLogger(__name__)

# Configuration DeepInfra
DEEPINFRA_API_KEY = os.getenv("DEEPINFRA_API_KEY")
LLM_MODEL = os.getenv("LLM_MODEL", "meta-llama/Meta-Llama-3-8B-Instruct")

# Configuration des embeddings
EMBEDDING_MODEL = "sentence-transformers/all-MiniLM-L6-v2"  # Modèle léger et performant

# Configuration du chunking pour le découpage des documents
CHUNK_SIZE = 800  # Taille maximale des chunks, optimisée pour préserver les FAQ
CHUNK_OVERLAP = 150  # Chevauchement entre chunks pour maintenir le contexte
SECTION_SEPARATOR = "⸻"  # Séparateur utilisé pour diviser les sections du document

# Configuration de la récupération des chunks pertinents
TOP_K_RETRIEVAL = 5  # Nombre maximum de chunks à récupérer pour une requête
SIMILARITY_THRESHOLD = 0.7  # Seuil de similarité pour filtrer les chunks pertinents

# Prompt système définissant le comportement du chatbot
SYSTEM_PROMPT = """
<|im_start|>system
Tu es un assistant expert pour Commodore Taxi Boat, une plateforme innovante de réservation de taxis maritimes. Ton rôle est de fournir des informations précises, professionnelles et conviviales aux utilisateurs (Clients, Établissements, Capitaines) en te basant exclusivement sur les informations du document Commodore.

Règles essentielles :
- Réponds uniquement en français, sauf demande explicite d'une autre langue.
- Structure systématiquement tes réponses en 3 parties :
  1. Réponse directe et concise à la question principale
  2. Détails complémentaires pertinents avec exemples concrets
  3. Étapes suivantes ou contact support si nécessaire
- Adapte ton ton et ton vocabulaire au profil de l'utilisateur (Client, Établissement, Capitaine).
- Si l'information n'est pas dans ton contexte, dis clairement : "Je n'ai pas d'information précise à ce sujet. Contactez <EMAIL> pour une assistance personnalisée."
- Mentionne les obligations légales (RGPD) et les initiatives environnementales (compensation carbone) lorsque c'est pertinent.
- Utilise des listes à puces ou numérotées pour les instructions en plusieurs étapes.
- Sois concis et précis, évite le jargon technique sauf avec les capitaines.
<|im_end|>

Exemples de réponses modèles :
- Question : "Comment payer ma course ?" (Client)
  Réponse :
  Vous pouvez payer votre course de deux façons :

  1. Par carte bancaire (Visa, Mastercard, Apple Pay) directement lors de la réservation
  2. Avec des crédits prépayés (100 € = 100 crédits) depuis votre compte

  Pour recharger vos crédits, accédez à la section "Mon solde" dans votre profil. Si vous rencontrez des difficultés, contactez notre support client à <EMAIL>.

- Question : "Quand suis-je payé ?" (Capitaine)
  Réponse :
  Vous êtes payé via Stripe Connect sous 7 jours ouvrés maximum après chaque course.

  Le paiement est effectué après déduction de la commission Commodore :
  • 20% pour les trajets simples
  • 10% pour les mises à disposition
  • 5% via le module intégré

  Vérifiez que vos informations bancaires sont correctement renseignées sur Stripe pour éviter tout retard de paiement.
"""

# Prompt de récupération pour formater la requête utilisateur avec contexte
RETRIEVAL_PROMPT = """
Contexte pertinent (classé par ordre de pertinence) :
{context}

Historique récent de la conversation :
{history}

Question actuelle de l'utilisateur (profil : {profile}) :
{question}

Instructions pour la réponse :
1. Réponds uniquement en français
2. Base ta réponse exclusivement sur le contexte fourni
3. Structure ta réponse avec une introduction directe, des détails pertinents et une conclusion/suggestion
4. Utilise des listes à puces ou numérotées pour les instructions en plusieurs étapes
5. Adapte ton ton au profil de l'utilisateur ({profile})
6. Sois concis et précis, évite le jargon technique sauf avec les capitaines
7. Si l'information n'est pas dans le contexte, indique-le clairement

Réponse (en français, formatée clairement) :
"""

class RagService:
    """
    Service pour la gestion du système RAG (Retrieval Augmented Generation) pour Commodore Taxi Boat.

    Ce service gère :
    - Le découpage des documents en chunks pour une récupération efficace.
    - La génération d'embeddings pour représenter les chunks sous forme de vecteurs.
    - La récupération des chunks pertinents en fonction des requêtes utilisateur.
    - La génération de réponses basées sur les chunks récupérés et le modèle de langage.

    Il est conçu pour un chatbot multicanal (web et mobile) répondant aux questions des Clients, Établissements et Capitaines.
    """

    def __init__(self):
        """
        Initialise le service RAG avec les modèles Gemini.

        Charge la clé API, configure les modèles d'embeddings et de chat, et prépare le text splitter pour le chunking.
        """
        # Vérifier que la clé API est définie
        if not os.environ.get('DEEPINFRA_API_TOKEN'):
            raise ValueError("DEEPINFRA_API_TOKEN non défini dans les variables d'environnement")

        # Initialiser le modèle d'embeddings
        self.embeddings = HuggingFaceEmbeddings(
            model_name=EMBEDDING_MODEL,
            cache_folder=".cache/huggingface"
        )

        # Initialiser le modèle de chat avec Meta-Llama-3
        self.llm = DeepInfra(
            model_id=LLM_MODEL,
            deepinfra_api_token=os.environ.get('DEEPINFRA_API_TOKEN'),
            model_kwargs={
                "temperature": 0.7,
                "max_tokens": 1024,
                "top_p": 0.9,
                "stop": ["<|im_end|>"],  # Arrêter la génération au marqueur de fin
                "repetition_penalty": 1.1  # Éviter les répétitions
            }
        )

        # Configurer le text splitter pour diviser les documents en chunks
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=CHUNK_SIZE,
            chunk_overlap=CHUNK_OVERLAP,
            length_function=len,
            separators=[SECTION_SEPARATOR, "\n\n", "\n", ".", " ", ""]
        )

        # Initialiser le vectorstore (sera chargé plus tard)
        self.vectorstore = None

    # Méthode _create_llm_model supprimée car elle n'est plus nécessaire avec DeepInfra

    def process_document(self, document: Document) -> None:
        """
        Traite un document en le découpant en chunks et en générant des embeddings.

        Args:
            document: Le document Django contenant le titre et le contenu à traiter.
        """
        logger.info(f"Traitement du document: {document.title}")

        # Diviser le document en sections basées sur le séparateur
        sections = document.content.split(SECTION_SEPARATOR)
        chunks = []
        for section_idx, section in enumerate(sections):
            section = section.strip()
            if not section:
                continue
            # Découper la section en chunks plus petits
            section_chunks = self.text_splitter.split_text(section)
            for chunk_idx, chunk_content in enumerate(section_chunks):
                # Ajouter des métadonnées pour identifier le profil et la section
                chunks.append({
                    "content": chunk_content,
                    "metadata": {
                        "section_idx": section_idx,
                        "section_title": section.split("\n")[0][:100],  # Utiliser la première ligne comme titre
                        "profile": self._infer_profile(section)
                    }
                })

        # Supprimer les chunks existants pour éviter les doublons
        document.chunks.all().delete()

        # Créer et sauvegarder les nouveaux chunks
        for i, chunk_data in enumerate(chunks):
            chunk = DocumentChunk.objects.create(
                document=document,
                content=chunk_data["content"],
                chunk_index=i,
                metadata=chunk_data["metadata"]
            )

            # Fonction interne pour générer l'embedding avec retry
            @retrying.retry(
                wait_exponential_multiplier=1000,
                wait_exponential_max=10000,
                stop_max_attempt_number=3
            )
            def generate_embedding(chunk_content):
                return self.embeddings.embed_query(chunk_content)

            try:
                # Générer et sauvegarder l'embedding
                embedding = generate_embedding(chunk_data["content"])
                chunk.embedding = embedding
                chunk.embedding_generated = True
                chunk.embedding_updated_at = timezone.now()
                chunk.save()
                logger.info(f"Embedding généré pour le chunk {i}")
            except Exception as e:
                logger.error(f"Erreur lors de la génération de l'embedding pour le chunk {i}: {str(e)}")

        # Mettre à jour le statut du document
        document.embedding_generated = True
        document.embedding_updated_at = timezone.now()
        document.save()

        logger.info(f"Traitement du document terminé: {document.title}")

    def _infer_profile(self, section: str) -> str:
        """
        Détermine le profil (Client, Établissement, Capitaine) d'une section.

        Args:
            section: Le contenu de la section à analyser.

        Returns:
            str: Le profil inféré ou "Général" si non déterminé.
        """
        section_lower = section.lower()
        if "client" in section_lower:
            return "Client"
        elif "établissement" in section_lower:
            return "Établissement"
        elif "capitaine" in section_lower:
            return "Capitaine"
        return "Général"

    def _load_vectorstore(self) -> None:
        """
        Charge les chunks avec embeddings dans un vectorstore FAISS pour la recherche vectorielle.
        """
        logger.info("Chargement du vectorstore FAISS")

        # Récupérer tous les chunks avec embeddings générés
        chunks = DocumentChunk.objects.filter(embedding_generated=True)
        if not chunks.exists():
            logger.warning("Aucun chunk avec embedding trouvé")
            self.vectorstore = None
            return

        # Approche sécurisée pour créer le vectorstore
        try:
            # Préparer les données pour FAISS
            texts = []
            metadatas = []
            embeddings_list = []

            # Convertir les chunks en format approprié pour FAISS
            for chunk in chunks:
                # Vérifier que le contenu est une chaîne valide
                if not isinstance(chunk.content, str) or not chunk.content.strip():
                    logger.warning(f"Chunk {chunk.id} ignoré: contenu invalide")
                    continue

                # Vérifier que l'embedding est valide
                if not chunk.embedding or not isinstance(chunk.embedding, list):
                    logger.warning(f"Chunk {chunk.id} ignoré: embedding invalide")
                    continue

                # Ajouter les données validées
                texts.append(chunk.content)
                metadatas.append({
                    "document_id": str(chunk.document.id),
                    "document_title": chunk.document.title,
                    "chunk_id": str(chunk.id),
                    "chunk_index": chunk.chunk_index,
                    "section_idx": chunk.metadata.get("section_idx", 0),
                    "section_title": chunk.metadata.get("section_title", ""),
                    "profile": chunk.metadata.get("profile", "Général")
                })
                embeddings_list.append(chunk.embedding)

            # Vérifier qu'il y a des données valides
            if not texts or not embeddings_list:
                logger.warning("Aucun chunk valide trouvé pour créer le vectorstore")
                self.vectorstore = None
                return

            # Approche simplifiée : utiliser la méthode from_embeddings
            from langchain_community.vectorstores.utils import DistanceStrategy

            # Créer le vectorstore FAISS
            text_embeddings = list(zip(texts, embeddings_list))
            self.vectorstore = FAISS.from_embeddings(
                text_embeddings=text_embeddings,
                embedding=self.embeddings,
                metadatas=metadatas,
                distance_strategy=DistanceStrategy.COSINE
            )

            logger.info(f"Vectorstore FAISS chargé avec {len(texts)} documents")

        except Exception as e:
            logger.error(f"Erreur lors du chargement du vectorstore: {str(e)}")
            import traceback
            traceback.print_exc()
            self.vectorstore = None

    def retrieve_relevant_chunks(self, query: str, user_profile: Optional[str] = None) -> List[Tuple[DocumentChunk, float]]:
        """
        Récupère les chunks les plus pertinents pour une requête, en tenant compte du profil utilisateur.
        Utilise une recherche sémantique avancée avec embeddings.

        Args:
            query: La requête textuelle de l'utilisateur.
            user_profile: Le profil de l'utilisateur (Client, Établissement, Capitaine), optionnel.

        Returns:
            Liste de tuples (chunk, score) triés par pertinence.
        """
        start_time = time.time()
        logger.info(f"Récupération des chunks pertinents pour la requête: {query} (profil: {user_profile})")

        # Vérifier si le résultat est en cache
        cache_key = f"rag_query_{hashlib.md5(query.encode()).hexdigest()}_{user_profile}"
        try:
            rag_cache = caches['rag']
            cached_result = rag_cache.get(cache_key)
            if cached_result:
                logger.info(f"Résultat récupéré depuis le cache RAG en {time.time() - start_time:.2f} secondes")
                return cached_result
        except Exception as e:
            logger.warning(f"Erreur lors de l'accès au cache RAG: {str(e)}")
            # Fallback au cache par défaut
            cached_result = cache.get(cache_key)
            if cached_result:
                logger.info(f"Résultat récupéré depuis le cache par défaut en {time.time() - start_time:.2f} secondes")
                return cached_result

        # Charger le vectorstore si nécessaire
        if self.vectorstore is None:
            vectorstore_start = time.time()
            self._load_vectorstore()
            logger.debug(f"Chargement du vectorstore en {time.time() - vectorstore_start:.2f} secondes")

        if self.vectorstore is None:
            logger.warning("Vectorstore non disponible")
            return []

        try:
            # Analyser la requête pour détecter les intentions et entités
            analysis_start = time.time()
            intent, entities = self._analyze_query(query)
            logger.debug(f"Analyse de la requête en {time.time() - analysis_start:.2f} secondes")
            logger.info(f"Intention détectée: {intent}, Entités: {entities}")

            # Générer l'embedding pour la requête
            embedding_start = time.time()
            query_embedding = self.embeddings.embed_query(query)
            logger.debug(f"Génération de l'embedding en {time.time() - embedding_start:.2f} secondes")

            # Optimisation: Ajuster les paramètres en fonction de l'intention
            # Réduire le nombre de résultats pour les questions spécifiques
            threshold = SIMILARITY_THRESHOLD
            k = TOP_K_RETRIEVAL

            if intent == "specific":
                threshold = 0.75  # Plus strict pour les questions spécifiques
                k = min(TOP_K_RETRIEVAL * 2, 10)  # Limiter à 10 résultats maximum
            elif intent == "general":
                threshold = 0.65  # Plus souple pour les questions générales
                k = min(TOP_K_RETRIEVAL, 5)  # Limiter à 5 résultats pour les questions générales

        except Exception as e:
            logger.error(f"Erreur lors de la génération de l'embedding pour la requête: {str(e)}")
            return []

        # Effectuer une recherche de similarité avancée
        search_start = time.time()
        try:
            # Vérifier quelle méthode est disponible
            if hasattr(self.vectorstore, 'similarity_search_by_vector_with_relevance_scores'):
                # Méthode standard
                results = self.vectorstore.similarity_search_by_vector_with_relevance_scores(
                    query_embedding,
                    k=k,
                    score_threshold=threshold
                )
            elif hasattr(self.vectorstore, 'similarity_search_with_score_by_vector'):
                # Méthode alternative
                results = self.vectorstore.similarity_search_with_score_by_vector(
                    query_embedding,
                    k=k
                )
                # Filtrer par seuil de similarité
                results = [(doc, score) for doc, score in results if score >= threshold]
            else:
                # Fallback à la recherche simple
                docs = self.vectorstore.similarity_search_by_vector(
                    query_embedding,
                    k=k
                )
                # Pas de scores disponibles, utiliser des scores fictifs
                results = [(doc, 1.0) for doc in docs]
                logger.warning("Utilisation de la recherche simple sans scores")

            logger.debug(f"Recherche vectorielle en {time.time() - search_start:.2f} secondes")
        except Exception as e:
            logger.error(f"Erreur lors de la recherche vectorielle: {str(e)}")
            return []

        # Optimisation: Précharger tous les chunks en une seule requête
        processing_start = time.time()
        chunk_ids = []
        doc_scores = {}
        doc_metadata = {}

        for doc, score in results:
            if hasattr(doc, 'metadata') and doc.metadata and doc.metadata.get("chunk_id"):
                chunk_id = doc.metadata.get("chunk_id")
                chunk_ids.append(chunk_id)
                doc_scores[chunk_id] = score
                doc_metadata[chunk_id] = doc.metadata

        # Récupérer tous les chunks en une seule requête
        chunks_dict = {}
        if chunk_ids:
            chunks = DocumentChunk.objects.filter(id__in=chunk_ids).select_related('document')
            chunks_dict = {str(chunk.id): chunk for chunk in chunks}

        logger.debug(f"Récupération des chunks en {time.time() - processing_start:.2f} secondes")

        # Traiter les résultats
        relevant_chunks = []
        for chunk_id in chunk_ids:
            try:
                if chunk_id not in chunks_dict:
                    logger.warning(f"Chunk {chunk_id} non trouvé dans la base de données")
                    continue

                chunk = chunks_dict[chunk_id]
                score = doc_scores[chunk_id]
                metadata = doc_metadata[chunk_id]
                chunk_profile = metadata.get("profile", "Général")

                # Facteurs d'ajustement du score
                profile_factor = 1.0
                content_factor = 1.0
                recency_factor = 1.0

                # Ajuster le score pour prioriser les chunks correspondant au profil
                if user_profile:
                    if chunk_profile == user_profile:
                        profile_factor = 1.2  # Bonus pour le profil exact
                    elif chunk_profile == "Général":
                        profile_factor = 1.0  # Neutre pour le profil général
                    else:
                        profile_factor = 0.8  # Pénalité pour les autres profils

                # Ajuster le score en fonction du contenu (présence d'entités)
                if entities:
                    # Optimisation: Utiliser une recherche plus efficace pour les entités
                    chunk_content_lower = chunk.content.lower()
                    entity_matches = sum(1 for entity in entities if entity.lower() in chunk_content_lower)
                    if entity_matches > 0:
                        content_factor = 1.0 + (0.1 * entity_matches)  # Bonus pour chaque entité trouvée

                # Ajuster le score en fonction de la récence (si disponible)
                if hasattr(chunk, 'updated_at') and chunk.updated_at:
                    from django.utils import timezone
                    days_old = (timezone.now() - chunk.updated_at).days
                    if days_old < 30:
                        recency_factor = 1.1  # Bonus pour les contenus récents

                # Score final ajusté
                adjusted_score = score * profile_factor * content_factor * recency_factor

                relevant_chunks.append((chunk, adjusted_score))
            except Exception as e:
                logger.warning(f"Erreur lors du traitement d'un résultat de recherche: {str(e)}")
                continue

        # Trier et limiter les résultats
        relevant_chunks = sorted(relevant_chunks, key=lambda x: x[1], reverse=True)[:TOP_K_RETRIEVAL]

        # Mettre en cache les résultats
        try:
            rag_cache = caches['rag']
            rag_cache.set(cache_key, relevant_chunks)  # Utilise le timeout défini dans settings.py (24h)
        except Exception as e:
            logger.warning(f"Erreur lors de l'accès au cache RAG: {str(e)}")
            # Fallback au cache par défaut
            cache.set(cache_key, relevant_chunks, timeout=86400)  # 24 heures

        total_time = time.time() - start_time
        logger.info(f"Récupération terminée: {len(relevant_chunks)} chunks pertinents trouvés en {total_time:.2f} secondes")
        return relevant_chunks

    def _analyze_query(self, query: str) -> Tuple[str, List[str]]:
        """
        Analyse la requête pour détecter l'intention et les entités.

        Args:
            query: La requête textuelle de l'utilisateur.

        Returns:
            Tuple (intention, liste d'entités)
        """
        # Mots-clés pour chaque catégorie
        specific_keywords = ["comment", "où", "quand", "qui", "pourquoi", "combien", "quel", "quelle", "quels", "quelles"]
        general_keywords = ["information", "aide", "besoin", "question", "savoir", "comprendre"]

        # Entités importantes pour Commodore
        entity_patterns = {
            "paiement": ["payer", "paiement", "carte", "crédit", "remboursement", "prix", "tarif", "coût"],
            "réservation": ["réserver", "réservation", "commander", "commande", "annuler", "annulation"],
            "qr_code": ["qr", "code", "scanner", "scan"],
            "bateau": ["bateau", "taxi", "navette", "embarcation", "capitaine"],
            "compte": ["compte", "profil", "inscription", "connexion", "mot de passe"]
        }

        # Déterminer l'intention
        query_lower = query.lower()
        if any(keyword in query_lower for keyword in specific_keywords):
            intent = "specific"
        elif any(keyword in query_lower for keyword in general_keywords):
            intent = "general"
        else:
            intent = "unknown"

        # Extraire les entités
        entities = []
        for entity_type, patterns in entity_patterns.items():
            if any(pattern in query_lower for pattern in patterns):
                entities.append(entity_type)
                # Ajouter les mots spécifiques trouvés
                for pattern in patterns:
                    if pattern in query_lower and len(pattern) > 3:  # Ignorer les mots trop courts
                        entities.append(pattern)

        return intent, entities

    def _select_response_method(self, query: str) -> str:
        """
        Sélectionne la méthode de réponse appropriée en fonction de la requête.

        Args:
            query: La requête de l'utilisateur

        Returns:
            La méthode à utiliser: 'llm', 'rules', 'offline'
        """
        query_lower = query.lower()

        # Vérifier si c'est une question de présentation
        if any(kw in query_lower for kw in ["présente", "présentation", "qui es-tu", "qui êtes-vous"]):
            return 'introduction'

        # Vérifier si c'est une question sur les fonctionnalités
        if any(kw in query_lower for kw in ["fonctionnalité", "que peux-tu faire", "aide-moi", "comment m'aider"]):
            return 'capabilities'

        # Vérifier si c'est une question sur le QR code
        if any(kw in query_lower for kw in ["qr", "qr code", "code", "scanner", "scan"]):
            return 'qr_code'

        # Vérifier si c'est une question sur le paiement
        if any(kw in query_lower for kw in ["payer", "paiement", "course", "carte", "crédit", "prépayé", "solde", "prix"]):
            return 'payment'

        # Vérifier si c'est une question sur la réservation
        if any(kw in query_lower for kw in ["réserver", "réservation", "commander", "commande", "bateau", "taxi"]):
            return 'reservation'

        # Vérifier si c'est une question sur l'annulation
        if any(kw in query_lower for kw in ["annuler", "annulation", "remboursement", "remboursé", "rembourser", "annule"]):
            return 'cancellation'

        # Par défaut, utiliser le LLM
        return 'llm'

    def _get_predefined_response(self, method: str, user_profile: Optional[str] = None) -> str:
        """
        Renvoie une réponse prédéfinie en fonction de la méthode et du profil utilisateur.

        Args:
            method: La méthode de réponse
            user_profile: Le profil de l'utilisateur

        Returns:
            La réponse prédéfinie
        """
        if method == 'introduction':
            if user_profile == "Client":
                return """
Bonjour ! Je suis l'assistant virtuel de Commodore Taxi Boat, spécialisé pour les clients.

Je peux vous aider à :
• Réserver un bateau-taxi
• Gérer vos réservations existantes
• Comprendre les options de paiement
• Répondre à vos questions sur le service

N'hésitez pas à me poser des questions spécifiques sur votre trajet maritime !
"""
            elif user_profile == "Capitaine":
                return """
Bonjour Capitaine ! Je suis l'assistant virtuel de Commodore Taxi Boat.

Je peux vous aider à :
• Gérer vos courses
• Comprendre le système de paiement
• Optimiser votre planning
• Résoudre les problèmes techniques

Comment puis-je vous assister aujourd'hui dans votre activité de Capitaine ?
"""
            else:
                return """
Bonjour ! Je suis l'assistant virtuel de Commodore Taxi Boat.

Je peux vous aider avec toutes vos questions concernant notre service de taxi-boat.
Que vous soyez client, capitaine ou établissement partenaire, je suis là pour vous guider.

Comment puis-je vous aider aujourd'hui ?
"""

        elif method == 'capabilities':
            return """
Je peux vous aider avec de nombreuses questions concernant Commodore Taxi Boat :

• **Réservations** : Comment réserver, modifier ou annuler une course
• **Paiements** : Options de paiement, crédits prépayés, remboursements
• **Embarquement** : QR code, procédure d'embarquement, bagages
• **Capitaines** : Certification, évaluation, pourboires
• **Établissements** : Partenariats, intégration, commission

Je peux également vous fournir des informations sur nos conditions générales, notre politique de confidentialité et nos initiatives environnementales.

Quelle information recherchez-vous aujourd'hui ?
"""

        elif method == 'qr_code':
            return """
Sans QR code, le Capitaine n'a pas le droit de vous embarquer. Le QR code est obligatoire car il permet de :
- Vérifier que la course est bien payée
- Lutter contre les fraudes
- Garantir une prise en charge sécurisée

Vous pouvez retrouver votre QR code dans la section "Mes réservations" de l'application Commodore, sur l'écran de confirmation de course, ou dans la notification reçue avant l'arrivée du bateau.
"""

        elif method == 'payment':
            return """
Pour payer votre course sur Commodore, vous avez deux options principales:

1. Le paiement classique par carte bancaire (Visa, Mastercard, Apple Pay...) au moment de chaque réservation
2. L'achat de crédits prépayés (ex. : 100 € = 100 crédits) qui seront automatiquement débités à chaque course

Les crédits vous permettent de gagner du temps à chaque réservation, de réserver plus rapidement sans sortir votre carte, et de mieux maîtriser votre budget.

Pour recharger vos crédits, allez dans votre profil Commodore > "Mon solde". Vous pouvez aussi activer un rechargement automatique quand votre solde descend sous un certain seuil.
"""

        elif method == 'reservation':
            return """
Pour réserver un bateau-taxi sur Commodore, suivez ces étapes simples:

1. Ouvrez l'application Commodore
2. Sélectionnez votre point de départ et d'arrivée sur la carte
3. Choisissez l'heure de départ souhaitée
4. Sélectionnez le nombre de passagers
5. Vérifiez le prix et confirmez votre réservation
6. Payez avec votre carte bancaire ou vos crédits prépayés

Vous recevrez une confirmation par email et notification. Un QR code vous sera fourni, que vous devrez présenter au Capitaine lors de l'embarquement.
"""

        elif method == 'cancellation':
            return """
Pour annuler une réservation sur Commodore:

1. Allez dans "Mes réservations" dans l'application
2. Sélectionnez la réservation à annuler
3. Appuyez sur "Annuler la réservation"

Politique d'annulation:
- Annulation plus de 24h avant le départ: remboursement à 100%
- Entre 24h et 2h avant le départ: remboursement à 50%
- Moins de 2h avant le départ: aucun remboursement

Le remboursement sera effectué sur votre moyen de paiement initial ou en crédits Commodore selon votre choix.
"""

        # Fallback
        return None

    def generate_response(self, session: ChatSession, user_message: str, user_profile: Optional[str] = None) -> str:
        """
        Génère une réponse à un message utilisateur en utilisant le système RAG avancé.
        Utilise la recherche sémantique et l'analyse d'intention pour des réponses plus précises.

        Args:
            session: La session de chat Django.
            user_message: Le message texte de l'utilisateur.
            user_profile: Le profil de l'utilisateur (Client, Établissement, Capitaine), optionnel.

        Returns:
            La réponse générée par le modèle.
        """
        logger.info(f"Génération de réponse pour la session {session.id} (profil: {user_profile})")

        try:
            # Vérifier si la réponse est en cache
            cache_key = f"rag_response_{session.id}_{user_message}_{user_profile}"
            try:
                rag_cache = caches['rag']
                cached_response = rag_cache.get(cache_key)
                if cached_response:
                    logger.info("Réponse récupérée depuis le cache RAG")
                    return cached_response
            except Exception as e:
                logger.warning(f"Erreur lors de l'accès au cache RAG: {str(e)}")
                # Fallback au cache par défaut
                cached_response = cache.get(cache_key)
                if cached_response:
                    logger.info("Réponse récupérée depuis le cache par défaut")
                    return cached_response

            # Sélectionner la méthode de réponse appropriée
            method = self._select_response_method(user_message)
            logger.info(f"Méthode de réponse sélectionnée: {method}")

            # Vérifier si nous avons une réponse prédéfinie pour cette méthode
            predefined_response = self._get_predefined_response(method, user_profile)

            # Analyser la requête pour détecter l'intention et les entités
            intent, entities = self._analyze_query(user_message)
            logger.info(f"Intention détectée: {intent}, Entités: {entities}")

            # Enregistrer le message utilisateur
            user_msg = ChatMessage.objects.create(
                session=session,
                role=ChatMessage.ROLE_USER,
                content=user_message
            )

            # Si nous avons une réponse prédéfinie et que ce n'est pas une méthode LLM, l'utiliser
            if predefined_response and method != 'llm':
                logger.info(f"Utilisation d'une réponse prédéfinie pour la méthode: {method}")

                # Post-traiter la réponse prédéfinie
                response = self._post_process_response(predefined_response, intent, entities, user_profile)

                # Enregistrer la réponse dans la session
                assistant_msg = ChatMessage.objects.create(
                    session=session,
                    role=ChatMessage.ROLE_ASSISTANT,
                    content=response
                )

                # Ajouter des chunks génériques comme sources
                from rag.models import Document
                docs = Document.objects.filter(embedding_generated=True)
                if docs.exists():
                    doc = docs.first()
                    chunks = doc.chunks.all()[:3]  # Prendre les 3 premiers chunks pour simplifier
                    for chunk in chunks:
                        assistant_msg.retrieved_documents.add(chunk)

                # Mettre en cache la réponse
                try:
                    rag_cache = caches['rag']
                    rag_cache.set(cache_key, response)
                except Exception as e:
                    logger.warning(f"Erreur lors de l'accès au cache RAG: {str(e)}")
                    # Fallback au cache par défaut
                    cache.set(cache_key, response, timeout=86400)  # 24 heures

                logger.info(f"Réponse prédéfinie générée avec succès pour la session {session.id}")
                return response

            # Récupérer les chunks pertinents avec la recherche sémantique améliorée
            relevant_chunks = self.retrieve_relevant_chunks(user_message, user_profile)

            # Gérer le cas où aucun chunk pertinent n'est trouvé
            if not relevant_chunks:
                logger.warning("Aucun chunk pertinent trouvé pour la requête")

                # Essayer une recherche plus large si aucun résultat n'est trouvé
                fallback_chunks = self._fallback_search(user_message, user_profile)

                if not fallback_chunks:
                    # Réponse par défaut si aucune information n'est trouvée
                    response = "Je suis désolé, mais je n'ai pas trouvé d'informations pertinentes pour répondre à votre question. Contactez <EMAIL> pour plus de détails."
                    assistant_msg = ChatMessage.objects.create(
                        session=session,
                        role=ChatMessage.ROLE_ASSISTANT,
                        content=response
                    )
                    try:
                        rag_cache = caches['rag']
                        rag_cache.set(cache_key, response)
                    except Exception as e:
                        logger.warning(f"Erreur lors de l'accès au cache RAG: {str(e)}")
                        # Fallback au cache par défaut
                        cache.set(cache_key, response, timeout=86400)  # 24 heures
                    return response
                else:
                    # Utiliser les résultats de la recherche de secours
                    relevant_chunks = fallback_chunks
                    logger.info(f"Utilisation de la recherche de secours: {len(relevant_chunks)} chunks trouvés")

            # Construire le contexte à partir des chunks récupérés, avec pondération par pertinence
            weighted_chunks = []
            for chunk, score in relevant_chunks:
                # Formater le chunk avec son score pour un meilleur contexte
                formatted_chunk = f"Section {chunk.metadata.get('section_title', 'Inconnue')} (pertinence: {score:.2f}):\n{chunk.content}"
                weighted_chunks.append((formatted_chunk, score))

            # Trier les chunks par score de pertinence et les joindre
            weighted_chunks.sort(key=lambda x: x[1], reverse=True)
            context = "\n\n".join([chunk for chunk, _ in weighted_chunks])

            # Récupérer l'historique récent de la conversation (augmenté à 20 messages pour un meilleur contexte)
            history_messages = session.messages.exclude(id=user_msg.id).order_by('created_at')[:20]

            # Formater l'historique avec plus de détails pour améliorer le contexte
            history_entries = []
            for msg in history_messages:
                timestamp = msg.created_at.strftime("%H:%M:%S")
                role = "Utilisateur" if msg.role == ChatMessage.ROLE_USER else "Assistant"
                history_entries.append(f"{role} ({timestamp}): {msg.content}")

            history = "\n\n".join(history_entries)

            # Adapter le prompt en fonction de l'intention détectée
            if intent == "specific":
                # Prompt plus précis pour les questions spécifiques
                retrieval_prompt = """
                Contexte (sections pertinentes, triées par pertinence):
                {context}

                Historique de la conversation:
                {history}

                Question spécifique de l'utilisateur (profil {profile}):
                {question}

                Réponse (en français, formatée clairement et précisément, en répondant directement à la question):
                """
            else:
                # Prompt standard pour les autres types de questions
                retrieval_prompt = RETRIEVAL_PROMPT

            # Préparer les messages pour le modèle
            messages = [
                {"role": "system", "content": SYSTEM_PROMPT},
                {"role": "user", "content": retrieval_prompt.format(
                    context=context,
                    history=history,
                    question=user_message,
                    section=", ".join(set(chunk.metadata.get("section_title", "Inconnue") for chunk, _ in relevant_chunks)),
                    profile=user_profile or "Inconnu"
                )}
            ]

            # Fonction interne pour générer la réponse avec retry
            @retrying.retry(
                wait_exponential_multiplier=1000,
                wait_exponential_max=10000,
                stop_max_attempt_number=3
            )
            def generate_response(msgs):
                # Formater les messages pour Llama
                formatted_prompt = ""
                for msg in msgs:
                    role = "system" if msg["role"] == "system" else "user" if msg["role"] == "user" else "assistant"
                    formatted_prompt += f"<|im_start|>{role}\n{msg['content']}\n<|im_end|>\n"
                
                try:
                    # Générer la réponse avec DeepInfra Llama
                    response = self.llm.invoke(formatted_prompt)
                    # Nettoyer la réponse
                    response = response.replace("<|im_start|>assistant\n", "").replace("<|im_end|>", "").strip()
                    return response
                except Exception as e:
                    logger.error(f"Erreur lors de la génération de la réponse avec Llama: {str(e)}")
                    return "Je suis désolé, mais j'ai rencontré une erreur. Veuillez réessayer."

            # Générer la réponse
            response = generate_response(messages)

            # Note: Une implémentation plus avancée avec sélection de modèle pourrait être ajoutée ici
            # dans une future mise à jour, après des tests approfondis

            # Post-traitement de la réponse
            response = self._post_process_response(response, intent, entities, user_profile)

            # Enregistrer la réponse de l'assistant
            assistant_msg = ChatMessage.objects.create(
                session=session,
                role=ChatMessage.ROLE_ASSISTANT,
                content=response
            )

            # Enregistrer les chunks utilisés pour la réponse
            for chunk, _ in relevant_chunks:
                assistant_msg.retrieved_documents.add(chunk)

            # Mettre en cache la réponse
            try:
                rag_cache = caches['rag']
                rag_cache.set(cache_key, response)

                # Mettre également en cache dans le cache offline pour le support hors ligne
                try:
                    offline_cache = caches['offline']
                    offline_key = f"offline_qa_{intent}_{'-'.join(entities[:3] if entities else ['general'])}"
                    offline_cache.set(offline_key, {
                        'question': user_message,
                        'response': response,
                        'profile': user_profile,
                        'entities': entities,
                        'intent': intent
                    })
                except Exception as e:
                    logger.warning(f"Erreur lors de l'accès au cache offline: {str(e)}")
            except Exception as e:
                logger.warning(f"Erreur lors de l'accès au cache RAG: {str(e)}")
                # Fallback au cache par défaut
                cache.set(cache_key, response, timeout=86400)  # 24 heures
            logger.info(f"Réponse générée avec succès pour la session {session.id}")
            return response

        except Exception as e:
            logger.error(f"Erreur lors de la génération de la réponse: {str(e)}")
            import traceback
            traceback.print_exc()
            error_response = "Je suis désolé, mais j'ai rencontré une erreur. Veuillez réessayer <NAME_EMAIL>."
            ChatMessage.objects.create(
                session=session,
                role=ChatMessage.ROLE_ASSISTANT,
                content=error_response
            )
            return error_response

    def _fallback_search(self, query: str, user_profile: Optional[str] = None) -> List[Tuple[DocumentChunk, float]]:
        """
        Recherche de secours avec des paramètres plus souples quand la recherche principale ne donne pas de résultats.

        Args:
            query: La requête textuelle de l'utilisateur.
            user_profile: Le profil de l'utilisateur.

        Returns:
            Liste de tuples (chunk, score) triés par pertinence.
        """
        logger.info(f"Exécution de la recherche de secours pour: {query}")

        # Vérifier si le vectorstore est disponible
        if self.vectorstore is None:
            logger.warning("Vectorstore non disponible pour la recherche de secours")
            return []

        # Simplifier la requête en ne gardant que les mots-clés importants
        simplified_query = self._simplify_query(query)

        if not simplified_query:
            logger.warning("Requête simplifiée vide")
            return []

        try:
            # Générer l'embedding pour la requête simplifiée
            query_embedding = self.embeddings.embed_query(simplified_query)

            # Recherche avec un seuil de similarité plus bas
            try:
                # Vérifier quelle méthode est disponible
                if hasattr(self.vectorstore, 'similarity_search_by_vector_with_relevance_scores'):
                    # Méthode standard
                    results = self.vectorstore.similarity_search_by_vector_with_relevance_scores(
                        query_embedding,
                        k=TOP_K_RETRIEVAL * 3,
                        score_threshold=0.5  # Seuil plus bas pour la recherche de secours
                    )
                elif hasattr(self.vectorstore, 'similarity_search_with_score_by_vector'):
                    # Méthode alternative
                    results = self.vectorstore.similarity_search_with_score_by_vector(
                        query_embedding,
                        k=TOP_K_RETRIEVAL * 3
                    )
                    # Filtrer par seuil de similarité
                    results = [(doc, score) for doc, score in results if score >= 0.5]
                else:
                    # Fallback à la recherche simple
                    docs = self.vectorstore.similarity_search_by_vector(
                        query_embedding,
                        k=TOP_K_RETRIEVAL * 3
                    )
                    # Pas de scores disponibles, utiliser des scores fictifs
                    results = [(doc, 1.0) for doc in docs]
                    logger.warning("Utilisation de la recherche simple sans scores pour la recherche de secours")
            except Exception as e:
                logger.error(f"Erreur lors de la recherche vectorielle de secours: {str(e)}")
                return []

            relevant_chunks = []
            for doc, score in results:
                try:
                    # Vérifier que les métadonnées sont valides
                    if not hasattr(doc, 'metadata') or not doc.metadata:
                        logger.warning("Document sans métadonnées ignoré dans la recherche de secours")
                        continue

                    chunk_id = doc.metadata.get("chunk_id")
                    if not chunk_id:
                        logger.warning("Document sans chunk_id ignoré dans la recherche de secours")
                        continue

                    # Récupérer le chunk correspondant
                    chunk = DocumentChunk.objects.get(id=chunk_id)

                    # Appliquer un facteur de boost pour le profil utilisateur si spécifié
                    if user_profile and doc.metadata.get("profile") == user_profile:
                        score *= 1.2  # Bonus de 20% pour les chunks correspondant au profil

                    relevant_chunks.append((chunk, score))
                except DocumentChunk.DoesNotExist:
                    logger.warning(f"Chunk {chunk_id} non trouvé dans la base de données")
                    continue
                except Exception as e:
                    logger.warning(f"Erreur lors du traitement d'un résultat de recherche: {str(e)}")
                    continue

            # Trier et limiter les résultats
            sorted_chunks = sorted(relevant_chunks, key=lambda x: x[1], reverse=True)[:TOP_K_RETRIEVAL]
            logger.info(f"Recherche de secours: {len(sorted_chunks)} chunks pertinents trouvés")
            return sorted_chunks

        except Exception as e:
            logger.error(f"Erreur lors de la recherche de secours: {str(e)}")
            import traceback
            traceback.print_exc()
            return []

    def _simplify_query(self, query: str) -> str:
        """
        Simplifie une requête en extrayant les mots-clés importants.

        Args:
            query: La requête originale.

        Returns:
            Requête simplifiée.
        """
        # Liste de mots vides en français
        stopwords = ["le", "la", "les", "un", "une", "des", "du", "de", "ce", "cette", "ces",
                    "mon", "ma", "mes", "ton", "ta", "tes", "son", "sa", "ses", "notre", "nos",
                    "votre", "vos", "leur", "leurs", "je", "tu", "il", "elle", "nous", "vous",
                    "ils", "elles", "et", "ou", "mais", "donc", "car", "pour", "par", "avec",
                    "sans", "en", "dans", "sur", "sous", "avant", "après", "pendant", "comme",
                    "si", "que", "qui", "quoi", "dont", "où", "quand", "comment", "pourquoi"]

        # Extraire les mots-clés
        words = query.lower().split()
        keywords = [word for word in words if word not in stopwords and len(word) > 2]

        # Ajouter des mots importants même s'ils sont dans les stopwords
        important_words = ["comment", "où", "quand", "pourquoi", "qui", "quoi"]
        for word in important_words:
            if word in query.lower() and word not in keywords:
                keywords.append(word)

        return " ".join(keywords)

    def _post_process_response(self, response: str, intent: str, entities: List[str], user_profile: Optional[str] = None) -> str:
        """
        Post-traite la réponse générée pour améliorer sa qualité.

        Args:
            response: La réponse générée par le modèle.
            intent: L'intention détectée dans la requête.
            entities: Les entités détectées dans la requête.
            user_profile: Le profil de l'utilisateur.

        Returns:
            Réponse post-traitée.
        """
        start_time = time.time()
        logger.debug(f"Début du post-traitement de la réponse")

        # Vérifier si la réponse est vide ou trop courte
        if not response or len(response.strip()) < 10:
            return "Je suis désolé, mais je n'ai pas pu générer une réponse appropriée. Veuillez reformuler votre question <NAME_EMAIL> pour plus d'aide."

        # Nettoyer la réponse
        response = response.strip()

        # Vérifier si la réponse est déjà bien structurée
        has_bullet_points = "•" in response or "- " in response or any(f"{i}." in response for i in range(1, 10))
        has_paragraphs = response.count("\n\n") >= 1

        # Améliorer la structure si nécessaire
        if not has_paragraphs and len(response) > 150 and not has_bullet_points:
            # Diviser en paragraphes pour les longues réponses sans structure
            sentences = response.split(". ")
            if len(sentences) >= 3:
                first_part = ". ".join(sentences[:1]) + "."
                middle_part = ". ".join(sentences[1:-1]) + "."
                last_part = sentences[-1] if sentences[-1].endswith(".") else sentences[-1] + "."

                response = f"{first_part}\n\n{middle_part}\n\n{last_part}"

        # Ajouter une formule de politesse si absente
        polite_endings = ["merci", "bonne journée", "cordialement", "n'hésitez pas"]
        has_polite_ending = any(ending in response.lower() for ending in polite_endings)

        if not has_polite_ending:
            if intent == "specific":
                response = response.rstrip() + "\n\nN'hésitez pas si vous avez d'autres questions !"
            else:
                response = response.rstrip() + "\n\nN'hésitez pas à me contacter pour plus de détails."

        # Ajouter une mention RGPD si pertinent
        rgpd_keywords = ["données", "personnel", "information", "email", "téléphone", "compte", "confidentialité", "privé"]
        needs_rgpd = any(keyword in response.lower() for keyword in rgpd_keywords) or any(entity in entities for entity in rgpd_keywords)

        if needs_rgpd and "RGPD" not in response and "rgpd" not in response.lower():
            response += "\n\nConformément au RGPD, vos données personnelles sont traitées de manière sécurisée et ne sont conservées que pour la durée nécessaire au service."

        # Personnaliser selon le profil utilisateur
        if user_profile:
            if user_profile == "Capitaine" and "capitaine" not in response.lower():
                # Vérifier si la réponse commence par une phrase complète
                if response[0].isupper() and "." in response[:50]:
                    # Insérer après la première phrase
                    first_sentence_end = response.find(".")
                    response = response[:first_sentence_end+1] + " En tant que Capitaine Commodore, " + response[first_sentence_end+2:].lstrip()
                else:
                    response = f"En tant que Capitaine Commodore, {response[0].lower()}{response[1:]}"

            elif user_profile == "Établissement" and "établissement" not in response.lower():
                if response[0].isupper() and "." in response[:50]:
                    first_sentence_end = response.find(".")
                    response = response[:first_sentence_end+1] + " Pour votre établissement partenaire, " + response[first_sentence_end+2:].lstrip()
                else:
                    response = f"Pour votre établissement partenaire, {response[0].lower()}{response[1:]}"

            elif user_profile == "Client" and intent == "specific" and len(response) > 100:
                # Ajouter un conseil pratique pour les clients
                tips = [
                    "Astuce : Vous pouvez enregistrer vos lieux favoris dans l'application pour accélérer vos réservations futures.",
                    "Conseil : Activez les notifications pour être informé en temps réel de l'arrivée de votre bateau-taxi.",
                    "Bon à savoir : Le pourboire pour les capitaines est facultatif mais toujours apprécié pour un service exceptionnel."
                ]
                import random
                if random.random() < 0.3:  # 30% de chance d'ajouter un conseil
                    response += f"\n\n{random.choice(tips)}"

        # Vérifier la présence d'informations de contact si nécessaire
        contact_keywords = ["problème", "difficulté", "assistance", "aide", "support", "contacter"]
        needs_contact = any(keyword in response.lower() for keyword in contact_keywords)

        if needs_contact and "<EMAIL>" not in response:
            response += "\n\nPour toute assistance supplémentaire, contactez notre équipe à <EMAIL>."

        logger.debug(f"Post-traitement terminé en {time.time() - start_time:.2f} secondes")
        return response

    def get_offline_faqs(self, profile: Optional[str] = None, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Récupère les questions fréquentes pour le support hors ligne.

        Args:
            profile: Le profil de l'utilisateur (Client, Établissement, Capitaine), optionnel.
            limit: Nombre maximum de FAQ à retourner.

        Returns:
            Liste de dictionnaires contenant les questions et réponses fréquentes.
        """
        logger.info(f"Récupération des FAQ pour le support hors ligne (profil: {profile}, limit: {limit})")

        try:
            # Récupérer toutes les clés du cache offline
            offline_cache = caches['offline']
            all_keys = offline_cache.keys("offline_qa_*")
        except Exception as e:
            logger.warning(f"Erreur lors de l'accès au cache offline: {str(e)}")
            return self._generate_default_faqs(profile)

        # Si aucune clé n'est trouvée, générer des FAQ par défaut
        if not all_keys:
            return self._generate_default_faqs(profile)

        # Récupérer les FAQ du cache
        faqs = []
        for key in all_keys[:limit]:
            cached_data = offline_cache.get(key)
            if cached_data and (not profile or cached_data.get('profile') == profile or cached_data.get('profile') == 'Général'):
                faqs.append({
                    'question': cached_data.get('question', ''),
                    'response': cached_data.get('response', ''),
                    'intent': cached_data.get('intent', 'unknown'),
                    'entities': cached_data.get('entities', []),
                    'profile': cached_data.get('profile', 'Général')
                })

        # Trier les FAQ par pertinence (spécifiques d'abord, puis générales)
        faqs.sort(key=lambda x: (
            0 if x.get('profile') == profile else 1,
            0 if x.get('intent') == 'specific' else 1,
            len(x.get('entities', [])),
            -len(x.get('question', ''))
        ))

        return faqs[:limit]

    def _generate_default_faqs(self, profile: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Génère des FAQ par défaut pour le support hors ligne.

        Args:
            profile: Le profil de l'utilisateur.

        Returns:
            Liste de dictionnaires contenant les questions et réponses par défaut.
        """
        default_faqs = [
            {
                'question': 'Comment réserver un bateau-taxi ?',
                'response': 'Pour réserver un bateau-taxi sur Commodore, ouvrez l\'application, sélectionnez votre point de départ et d\'arrivée, choisissez l\'heure de départ, indiquez le nombre de passagers, vérifiez le prix et confirmez votre réservation. Vous recevrez une confirmation par email avec un QR code à présenter au capitaine.',
                'intent': 'specific',
                'entities': ['réservation', 'bateau', 'taxi'],
                'profile': 'Client'
            },
            {
                'question': 'Comment payer ma course ?',
                'response': 'Vous pouvez payer votre course sur Commodore par carte bancaire (Visa, Mastercard, Apple Pay) lors de la réservation ou utiliser des crédits prépayés. Pour recharger vos crédits, allez dans votre profil > "Mon solde".',
                'intent': 'specific',
                'entities': ['paiement', 'course', 'crédit'],
                'profile': 'Client'
            },
            {
                'question': 'Comment annuler ma réservation ?',
                'response': 'Pour annuler une réservation, allez dans "Mes réservations", sélectionnez la réservation à annuler et appuyez sur "Annuler". Les conditions de remboursement dépendent du délai : 100% si plus de 24h avant, 50% entre 24h et 2h, aucun remboursement si moins de 2h avant le départ.',
                'intent': 'specific',
                'entities': ['annulation', 'réservation', 'remboursement'],
                'profile': 'Client'
            },
            {
                'question': 'Où trouver mon QR code ?',
                'response': 'Vous pouvez retrouver votre QR code dans la section "Mes réservations" de l\'application Commodore, sur l\'écran de confirmation de course, ou dans la notification reçue avant l\'arrivée du bateau.',
                'intent': 'specific',
                'entities': ['qr_code', 'code', 'scanner'],
                'profile': 'Client'
            },
            {
                'question': 'Quand suis-je payé pour mes courses ?',
                'response': 'En tant que Capitaine, vous êtes payé via Stripe Connect sous 7 jours ouvrés maximum après la course, après déduction de la commission Commodore (20% pour les trajets simples, 10% pour les mises à disposition, 5% via le module intégré).',
                'intent': 'specific',
                'entities': ['paiement', 'capitaine', 'commission'],
                'profile': 'Capitaine'
            }
        ]

        # Filtrer par profil si spécifié
        if profile:
            default_faqs = [faq for faq in default_faqs if faq['profile'] == profile or faq['profile'] == 'Général']

        return default_faqs

    def export_offline_data(self) -> Dict[str, Any]:
        """
        Exporte les données pour le support hors ligne.

        Returns:
            Dictionnaire contenant les données pour le support hors ligne.
        """
        logger.info("Exportation des données pour le support hors ligne")

        # Récupérer les FAQ pour chaque profil
        client_faqs = self.get_offline_faqs(profile='Client', limit=20)
        captain_faqs = self.get_offline_faqs(profile='Capitaine', limit=10)
        establishment_faqs = self.get_offline_faqs(profile='Établissement', limit=10)
        general_faqs = self.get_offline_faqs(limit=10)

        # Récupérer les documents les plus importants
        documents = []
        for doc in Document.objects.filter(embedding_generated=True).order_by('-updated_at')[:5]:
            documents.append({
                'title': doc.title,
                'content': doc.content[:1000],  # Limiter la taille pour l'export
                'category': doc.category,
                'updated_at': doc.updated_at.isoformat() if doc.updated_at else None
            })

        # Créer le package de données hors ligne
        from django.utils import timezone
        offline_data = {
            'faqs': {
                'client': client_faqs,
                'captain': captain_faqs,
                'establishment': establishment_faqs,
                'general': general_faqs
            },
            'documents': documents,
            'version': '1.0',
            'generated_at': timezone.now().isoformat(),
            'expires_at': (timezone.now() + timezone.timedelta(days=7)).isoformat()
        }

        # Mettre en cache le package complet
        try:
            offline_cache = caches['offline']
            offline_cache.set('offline_data_package', offline_data)
        except Exception as e:
            logger.warning(f"Erreur lors de l'accès au cache offline: {str(e)}")

        return offline_data


# Instance singleton du service RAG
# Désactivé temporairement pour les tests de paiement
# rag_service = RagService()
rag_service = None  # Pour permettre l'importation sans erreur