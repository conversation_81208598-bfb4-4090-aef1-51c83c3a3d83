from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
# --- Authentification sociale supprimée temporairement ---
from . import views

app_name = 'authentication'

urlpatterns = [
    # Inscription
    path('register/', views.RegisterView.as_view(), name='register'),
    path('verify-email/', views.VerifyEmailView.as_view(), name='verify-email'),
    path('resend-verification-code/', views.ResendVerificationCodeView.as_view(), name='resend-verification-code'),
    path('login/', views.LoginView.as_view(), name='login'),
    # Réinitialisation du mot de passe
    path('password/reset/', views.PasswordResetRequestView.as_view(), name='password-reset-request'),
    path('password/reset/verify/', views.PasswordResetVerifyView.as_view(), name='password-reset-verify'),
    
    # Tokens JWT
    path('token/refresh/', TokenRefreshView.as_view(), name='token-refresh'),
]
