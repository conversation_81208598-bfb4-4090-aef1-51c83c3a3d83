# Generated by Django 4.2.8 on 2025-05-30 23:42

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
        ("trips", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Review",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("TRIP", "Course"),
                            ("CAPTAIN", "Capitaine"),
                            ("CLIENT", "Client"),
                            ("BOAT", "Bateau"),
                            ("ESTABLISHMENT", "Établissement"),
                        ],
                        max_length=20,
                        verbose_name="type",
                    ),
                ),
                ("object_id", models.PositiveIntegerField()),
                (
                    "rating",
                    models.IntegerField(
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                        verbose_name="note",
                    ),
                ),
                ("title", models.CharField(max_length=255, verbose_name="titre")),
                ("comment", models.TextField(verbose_name="commentaire")),
                ("pros", models.TextField(blank=True, verbose_name="points positifs")),
                ("cons", models.TextField(blank=True, verbose_name="points négatifs")),
                (
                    "cleanliness_rating",
                    models.IntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                        verbose_name="note de propreté",
                    ),
                ),
                (
                    "communication_rating",
                    models.IntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                        verbose_name="note de communication",
                    ),
                ),
                (
                    "punctuality_rating",
                    models.IntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                        verbose_name="note de ponctualité",
                    ),
                ),
                (
                    "value_rating",
                    models.IntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                        verbose_name="note qualité-prix",
                    ),
                ),
                (
                    "is_verified",
                    models.BooleanField(default=False, verbose_name="vérifié"),
                ),
                ("is_public", models.BooleanField(default=True, verbose_name="public")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="créé le"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="mis à jour le"),
                ),
                (
                    "reported_count",
                    models.PositiveIntegerField(
                        default=0, verbose_name="nombre de signalements"
                    ),
                ),
                (
                    "author",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="written_reviews",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "content_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                    ),
                ),
                (
                    "trip",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reviews",
                        to="trips.trip",
                    ),
                ),
            ],
            options={
                "verbose_name": "avis",
                "verbose_name_plural": "avis",
                "ordering": ["-created_at"],
                "unique_together": {("author", "content_type", "object_id", "trip")},
            },
        ),
        migrations.CreateModel(
            name="ReviewResponse",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("content", models.TextField(verbose_name="contenu")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="créé le"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="mis à jour le"),
                ),
                (
                    "author",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="review_responses",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "review",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="responses",
                        to="reviews.review",
                    ),
                ),
            ],
            options={
                "verbose_name": "réponse à un avis",
                "verbose_name_plural": "réponses aux avis",
                "ordering": ["created_at"],
            },
        ),
        migrations.CreateModel(
            name="ReviewReport",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "reason",
                    models.CharField(
                        choices=[
                            ("INAPPROPRIATE", "Contenu inapproprié"),
                            ("SPAM", "Spam"),
                            ("FAKE", "Avis frauduleux"),
                            ("OFFENSIVE", "Contenu offensant"),
                            ("OTHER", "Autre"),
                        ],
                        max_length=20,
                        verbose_name="raison",
                    ),
                ),
                ("description", models.TextField(verbose_name="description")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="créé le"),
                ),
                (
                    "status",
                    models.CharField(
                        default="PENDING", max_length=20, verbose_name="statut"
                    ),
                ),
                (
                    "resolved_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="résolu le"
                    ),
                ),
                (
                    "resolution_notes",
                    models.TextField(blank=True, verbose_name="notes de résolution"),
                ),
                (
                    "reporter",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="review_reports",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "review",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reports",
                        to="reviews.review",
                    ),
                ),
            ],
            options={
                "verbose_name": "signalement d'avis",
                "verbose_name_plural": "signalements d'avis",
                "ordering": ["-created_at"],
                "unique_together": {("review", "reporter")},
            },
        ),
    ]
