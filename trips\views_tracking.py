"""
Vues pour le suivi en temps réel des courses
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.utils import timezone
from decimal import Decimal
from .models import Trip, Location
from .serializers import TripSerializer
import json


class TripLocationUpdateView(APIView):
    """Mettre à jour la position du capitaine pendant une course"""
    permission_classes = [IsAuthenticated]

    def post(self, request, trip_id):
        trip = get_object_or_404(Trip, id=trip_id)
        
        # Vérifier que l'utilisateur est le capitaine de cette course
        if not hasattr(request.user, 'captain') or trip.captain != request.user.captain:
            return Response(
                {'error': 'Seul le capitaine assigné peut mettre à jour la position'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Vérifier que la course est en cours
        if trip.status != Trip.Status.IN_PROGRESS:
            return Response(
                {'error': 'La course doit être en cours pour mettre à jour la position'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Récupérer les données de position
        latitude = request.data.get('latitude')
        longitude = request.data.get('longitude')
        accuracy = request.data.get('accuracy')
        speed = request.data.get('speed')
        heading = request.data.get('heading')
        altitude = request.data.get('altitude')
        
        if not latitude or not longitude:
            return Response(
                {'error': 'Latitude et longitude sont requises'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Créer une nouvelle position
            location = Location.objects.create(
                trip=trip,
                latitude=Decimal(str(latitude)),
                longitude=Decimal(str(longitude)),
                accuracy=float(accuracy) if accuracy else None,
                speed=float(speed) if speed else None,
                heading=float(heading) if heading else None,
                altitude=float(altitude) if altitude else None
            )
            
            # Mettre à jour la position actuelle dans le trip
            trip.current_location = f"{latitude}, {longitude}"
            
            # Calculer l'ETA si possible
            eta = self._calculate_eta(trip, latitude, longitude)
            if eta:
                trip.estimated_arrival_time = eta
            
            trip.save()
            
            return Response({
                'message': 'Position mise à jour avec succès',
                'location': {
                    'id': location.id,
                    'latitude': location.latitude,
                    'longitude': location.longitude,
                    'timestamp': location.timestamp,
                    'speed': location.speed,
                    'heading': location.heading
                },
                'estimated_arrival_time': trip.estimated_arrival_time
            })
            
        except Exception as e:
            return Response(
                {'error': f'Erreur lors de la mise à jour: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    def _calculate_eta(self, trip, current_lat, current_lon):
        """Calcule l'heure d'arrivée estimée basée sur la position actuelle"""
        try:
            # Récupérer les coordonnées de destination
            # Pour une course simple, c'est arrival_location
            # Pour une mise à disposition, c'est le point de retour
            
            # Ici on peut implémenter un calcul plus sophistiqué
            # Pour l'instant, on retourne None
            return None
        except:
            return None


class TripTrackingView(APIView):
    """Suivre une course en temps réel"""
    permission_classes = [IsAuthenticated]

    def get(self, request, trip_id):
        trip = get_object_or_404(Trip, id=trip_id)
        
        # Vérifier les permissions
        is_client = hasattr(request.user, 'client') and trip.client == request.user.client
        is_captain = hasattr(request.user, 'captain') and trip.captain == request.user.captain
        
        if not (is_client or is_captain):
            return Response(
                {'error': 'Accès refusé'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Récupérer les dernières positions
        recent_locations = trip.locations.all()[:10]  # 10 dernières positions
        
        # Préparer les données de suivi
        tracking_data = {
            'trip_id': trip.id,
            'status': trip.status,
            'current_location': trip.current_location,
            'estimated_arrival_time': trip.estimated_arrival_time,
            'delay_minutes': trip.delay_minutes,
            'problem_description': trip.problem_description,
            'recent_locations': [
                {
                    'latitude': float(loc.latitude),
                    'longitude': float(loc.longitude),
                    'timestamp': loc.timestamp,
                    'speed': loc.speed,
                    'heading': loc.heading,
                    'accuracy': loc.accuracy
                }
                for loc in recent_locations
            ],
            'trip_info': {
                'start_location': trip.start_location,
                'end_location': trip.end_location,
                'scheduled_start_time': trip.scheduled_start_time,
                'actual_start_time': trip.actual_start_time,
                'captain_name': f"{trip.captain.user.first_name} {trip.captain.user.last_name}",
                'boat_name': trip.boat.name if trip.boat else None,
                'passenger_count': trip.passenger_count
            }
        }
        
        return Response(tracking_data)


class TripHistoryView(APIView):
    """Historique complet des positions d'une course"""
    permission_classes = [IsAuthenticated]

    def get(self, request, trip_id):
        trip = get_object_or_404(Trip, id=trip_id)
        
        # Vérifier les permissions
        is_client = hasattr(request.user, 'client') and trip.client == request.user.client
        is_captain = hasattr(request.user, 'captain') and trip.captain == request.user.captain
        
        if not (is_client or is_captain):
            return Response(
                {'error': 'Accès refusé'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Récupérer toutes les positions
        all_locations = trip.locations.all()
        
        history_data = {
            'trip_id': trip.id,
            'total_locations': all_locations.count(),
            'locations': [
                {
                    'id': loc.id,
                    'latitude': float(loc.latitude),
                    'longitude': float(loc.longitude),
                    'timestamp': loc.timestamp,
                    'speed': loc.speed,
                    'heading': loc.heading,
                    'accuracy': loc.accuracy,
                    'altitude': loc.altitude
                }
                for loc in all_locations
            ],
            'trip_summary': {
                'status': trip.status,
                'start_time': trip.actual_start_time,
                'end_time': trip.actual_end_time,
                'duration_minutes': trip.calculate_duration(),
                'total_distance': self._calculate_total_distance(all_locations)
            }
        }
        
        return Response(history_data)
    
    def _calculate_total_distance(self, locations):
        """Calcule la distance totale parcourue"""
        if len(locations) < 2:
            return 0
        
        total_distance = 0
        prev_location = None
        
        for location in locations:
            if prev_location:
                # Calcul de distance entre deux points
                distance = self._haversine_distance(
                    float(prev_location.latitude), float(prev_location.longitude),
                    float(location.latitude), float(location.longitude)
                )
                total_distance += distance
            prev_location = location
        
        return round(total_distance, 2)
    
    def _haversine_distance(self, lat1, lon1, lat2, lon2):
        """Calcule la distance entre deux points géographiques"""
        from math import radians, cos, sin, asin, sqrt
        
        # Convertir en radians
        lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
        
        # Formule de Haversine
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * asin(sqrt(a))
        r = 6371  # Rayon de la Terre en kilomètres
        
        return c * r


class TripNotesView(APIView):
    """Ajouter des notes pendant une course"""
    permission_classes = [IsAuthenticated]

    def patch(self, request, trip_id):
        trip = get_object_or_404(Trip, id=trip_id)
        
        # Vérifier les permissions
        is_client = hasattr(request.user, 'client') and trip.client == request.user.client
        is_captain = hasattr(request.user, 'captain') and trip.captain == request.user.captain
        
        if not (is_client or is_captain):
            return Response(
                {'error': 'Accès refusé'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        note = request.data.get('note', '')
        if not note:
            return Response(
                {'error': 'Une note est requise'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Ajouter la note avec timestamp
        timestamp = timezone.now().strftime('%Y-%m-%d %H:%M:%S')
        user_name = request.user.get_full_name() or request.user.email
        formatted_note = f"[{timestamp}] {user_name}: {note}"
        
        if is_captain:
            trip.captain_notes += f"\n{formatted_note}"
        else:
            trip.client_notes += f"\n{formatted_note}"
        
        trip.save()
        
        return Response({
            'message': 'Note ajoutée avec succès',
            'trip': TripSerializer(trip).data
        })
