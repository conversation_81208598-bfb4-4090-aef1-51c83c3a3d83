# API Endpoints - Accounts

## 1. Gestion des profils utilisateurs

### 1.1. Récupérer le profil utilisateur
- **Endpoint**: GET /api/profile/
- **Endpoint**: PATCH /api/profile/
- **Endpoint**: GET /api/clients/
- **Endpoint**: GET /api/captains/
- **Endpoint**: GET /api/establishments/
- **Endpoint**: GET /api/users/<id>/
- **Endpoint**: GET /api/captains/<id>/
- **Endpoint**: GET /api/establishments/<id>/
- **Endpoint**: GET /api/favorites/locations/
- **Endpoint**: POST /api/favorites/locations/
- **Endpoint**: GET /api/favorites/locations/<id>/
- **Endpoint**: GET /api/favorites/captains/
- **Endpoint**: POST /api/favorites/captains/
- **Endpoint**: GET /api/favorites/captains/<id>/

# Exemples d’utilisation

## Profil utilisateur
- **GET /api/profile/**
  - <PERSON><PERSON><PERSON> le profil de l’utilisateur connecté.
- **PATCH /api/profile/**
  - Met à jour le profil de l’utilisateur connecté.

### Exemple PATCH complet pour chaque type d’utilisateur

> **Note :** Le champ `profile_picture` (et autres images) doit être une URL d’image déjà hébergée (ex : AWS S3, CloudFront, CDN, etc). L’API n’accepte pas d’upload direct d’image via PATCH/JSON. Exemple d’URL valide :
> 
> `https://mes-images.s3.us-east-1.amazonaws.com/profil/utilisateur123.jpg`

**Client**
```json
{
  "first_name": "Jean",
  "last_name": "Dupont",
  "phone_number": "+***********",
  "profile_picture": "https://mes-images.s3.us-east-1.amazonaws.com/profil/jean.jpg",
  "client_profile": {
    "wallet_balance": 200.0,
    "date_of_birth": "1990-01-01",
    "nationality": "Française",
    "preferred_language": "fr",
    "emergency_contact_name": "Marie Dupont",
    "emergency_contact_phone": "+33687654321"
  }
}
```

**Captain**
```json
{
  "first_name": "CapitaineNom",
  "last_name": "LeGrand",
  "phone_number": "+33699999999",
  "profile_picture": "https://mes-images.s3.us-east-1.amazonaws.com/profil/capitaine.jpg",
  "captain_profile": {
    "experience": "10 ans",
    "average_rating": 4.8,
    "total_trips": 500,
    "wallet_balance": 1000.0,
    "is_available": true,
    "current_location": "Port de Marseille",
    "license_number": "CAPT-12345",
    "license_expiry_date": "2030-12-31",
    "years_of_experience": 15,
    "certifications": ["Yacht Master", "STCW"],
    "specializations": ["Pêche", "Tourisme"],
    "availability_status": "AVAILABLE",
    "boat_photos": ["https://example.com/boats/1.jpg"],
    "boat": {
      "name": "Nouveau Bateau",
      "capacity": 8,
      "type": "Voilier",
      "registration_number": "BOAT-98765"
    }
  }
}
```

**Établissement**
```json
{
  "first_name": "Alex",
  "last_name": "Dahoui",
  "phone_number": "+***********",
  "profile_picture": "https://mes-images.s3.us-east-1.amazonaws.com/profil/etab.jpg",
  "establishment_profile": {
    "name": "Plage Paradis",
    "type": "PRIVATE_BEACH",
    "address": "Route de la plage, Cotonou",
    "description": "Un lieu unique pour vos vacances.",
    "main_photo": "https://example.com/etab/main.jpg",
    "secondary_photos": ["https://example.com/etab/1.jpg", "https://example.com/etab/2.jpg"],
    "wallet_balance": 5000.0,
    "business_name": "SARL Paradis",
    "business_type": "Loisirs",
    "registration_number": "ETAB-123456",
    "tax_id": "TX-987654",
    "opening_hours": {"lundi": "08:00-22:00", "dimanche": "09:00-20:00"},
    "services_offered": ["Bar", "Restaurant", "Piscine"],
    "average_rating": 4.6,
    "location_coordinates": "6.3703,2.3912",
    "website": "https://plageparadis.com",
    "social_media": {"facebook": "plageparadis", "instagram": "@plageparadis"}
  }
}
```

## Listes
- **GET /api/clients/**
- **GET /api/captains/**
- **GET /api/establishments/**

## Détails
- **GET /api/users/<id>/**
- **GET /api/captains/<id>/**
- **GET /api/establishments/<id>/**

## Favoris
- **GET/POST /api/favorites/locations/**
- **GET /api/favorites/locations/<id>/**
- **GET/POST /api/favorites/captains/**
- **GET /api/favorites/captains/<id>/**

- **Description**: Récupérer les informations du profil de l'utilisateur connecté
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "id": 123,
  "email": "<EMAIL>",
  "phone_number": "+***********",
  "type": "CLIENT",
  "profile_picture": "https://example.com/profiles/utilisateur.jpg",
  "email_verified": true,
  "phone_verified": true,
  "created_at": "2025-01-15T10:30:00Z",
  "client": {
    "wallet_balance": 150.00,
    "date_of_birth": "1990-05-15",
    "preferred_payment_method": "WALLET",
    "favorite_locations": [
      {
        "name": "Domicile",
        "address": "23 rue de la Mer, 83990 Saint-Tropez"
      },
      {
        "name": "Bureau",
        "address": "45 avenue de la République, 83990 Saint-Tropez"
      }
    ]
  }
}
```

### 1.2. Mettre à jour le profil utilisateur
- **Endpoint**: PATCH /api/accounts/profile/
- **Description**: Mettre à jour les informations du profil utilisateur connecté (et uniquement les siennes)
- **Auth Required**: Oui (JWT Token)

#### Sécurité
- Seul l'utilisateur connecté (via le token) peut modifier son propre profil.
- L'utilisateur ne peut modifier que les champs correspondant à son propre type :
  - Un client ne peut modifier que ses champs de client.
  - Un capitaine ne peut modifier que ses champs de capitaine et de bateau.
  - Un établissement ne peut modifier que ses champs d'établissement.
- Il est impossible de modifier les données d'un autre utilisateur, même en modifiant l'id ou le type dans la requête.

#### Exemple de requête PATCH pour chaque type

**Client**
```json
{
  "first_name": "NouveauNom",
  "client_profile": {
    "date_of_birth": "1990-05-20",
    "preferred_language": "en"
  }
}
```

**Capitaine**
```json
{
  "first_name": "CapitaineNom",
  "captain_profile": {
    "experience": "10 ans",
    "boat": {
      "name": "Nouveau Bateau",
      "capacity": 8
    }
  }
}
```

**Établissement**
```json
{
  "establishment_profile": {
    "name": "Mon Hôtel",
    "address": "123 avenue du Port"
  }
}
```

- **Response (200 OK)**:
```json
{
  "message": "Profil mis à jour avec succès"
}
```

### 1.3. Récupérer le profil d'un utilisateur (client, capitaine, établissement)
- **Endpoint**: GET /api/users/{id}/
- **Description**: Récupérer les informations publiques du profil d’un utilisateur (client, capitaine, ou établissement) via le même endpoint.
- **Auth Required**: Oui (JWT Token)
- **Note :** Utilisez toujours `/api/users/{id}/` pour obtenir le détail d’un utilisateur, quel que soit son type (client, capitaine, établissement).
- **Response (200 OK)**:
```json
{
  "id": 123,
  "user": {
    "first_name": "Marie",
    "last_name": "Martin",
    "profile_picture": "https://example.com/profiles/marie.jpg"
  },
  "average_rating": 4.7,
  "total_trips": 25,
  "member_since": "2025-01-15T10:30:00Z"
}

```


  "user": {
    "first_name": "Jean",
    "last_name": "Dupont",
    "profile_picture": "https://example.com/profiles/jean.jpg"
  },
  "experience": "5 ans d'expérience en navigation, spécialisé dans les excursions côtières",
  "average_rating": 4.8,
  "total_trips": 350,
  "is_available": true,
  "years_of_experience": 5,
  "certifications": ["Permis Côtier", "Permis Hauturier", "Secourisme en Mer"],
  "specializations": ["Excursions côtières", "Plongée", "Pêche sportive"],
  "boats": [
    {
      "id": 1,
      "name": "Blue Wave",
      "registration_number": "FR123456",
      "color": "Bleu",
      "capacity": 8,
      "fuel_type": "DIESEL",
      "photos": [
        "https://example.com/boats/blue_wave_1.jpg"
      ]
    }
  ],
  "recent_reviews": [
    {
      "id": 45,
      "rating": 5,
      "comment": "Excellent capitaine, très professionnel",
      "author": "Marie M.",
      "created_at": "2025-05-20T15:30:45Z"
    }
  ]
}
```

### 1.5. Récupérer le profil d'un établissement
- **Endpoint**: GET /api/accounts/establishments/{id}/
- **Description**: Récupérer les informations publiques du profil d'un établissement
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "id": 3,
  "name": "Hôtel Riviera",
  "type": "HOTEL",
  "address": "123 Boulevard de la Mer, 83990 Saint-Tropez",
  "description": "Hôtel de luxe avec accès direct à la mer",
  "main_photo": "https://example.com/establishments/hotel_riviera_main.jpg",
  "secondary_photos": [
    "https://example.com/establishments/hotel_riviera_1.jpg",
    "https://example.com/establishments/hotel_riviera_2.jpg"
  ],
  "average_rating": 4.6,
  "opening_hours": {
    "monday": {"open": "08:00", "close": "22:00"},
    "tuesday": {"open": "08:00", "close": "22:00"},
    "wednesday": {"open": "08:00", "close": "22:00"},
    "thursday": {"open": "08:00", "close": "22:00"},
    "friday": {"open": "08:00", "close": "23:00"},
    "saturday": {"open": "08:00", "close": "23:00"},
    "sunday": {"open": "09:00", "close": "21:00"}
  },
  "services_offered": ["Restaurant", "Spa", "Plage privée", "Navettes maritimes"],
  "location_coordinates": "43.2707,6.6400",
  "website": "https://hotel-riviera-example.com",
  "social_media": {
    "facebook": "hotel.riviera",
    "instagram": "hotelriviera"
  },
  "boats": [
    {
      "id": 2,
      "name": "Riviera Express",
      "capacity": 12,
      "photos": [
        "https://example.com/boats/riviera_express_1.jpg"
      ]
    }
  ],
  "recent_reviews": [
    {
      "id": 46,
      "rating": 5,
      "comment": "Service impeccable et navettes maritimes très pratiques",
      "author": "Jean D.",
      "created_at": "2025-05-18T10:15:30Z"
    }
  ]
}
```

### 1.6. Liste des capitaines disponibles
- **Endpoint**: GET /api/accounts/captains/available/
- **Description**: Récupérer la liste des capitaines disponibles pour une période donnée
- **Auth Required**: Oui (JWT Token)
- **Query Parameters**:
  - start_time: Date et heure de début (format ISO 8601)
  - end_time: Date et heure de fin (format ISO 8601)
  - location: Localisation approximative (optionnel)
- **Response (200 OK)**:
```json
[
  {
    "id": 5,
    "user": {
      "first_name": "Jean",
      "last_name": "Dupont",
      "profile_picture": "https://example.com/profiles/jean.jpg"
    },
    "average_rating": 4.8,
    "years_of_experience": 5,
    "boats": [
      {
        "id": 1,
        "name": "Blue Wave",
        "capacity": 8,
        "photos": [
          "https://example.com/boats/blue_wave_1.jpg"
        ]
      }
    ]
  },
  // ...
]
```

### 1.7. Liste des établissements partenaires
- **Endpoint**: GET /api/accounts/establishments/
- **Description**: Récupérer la liste des établissements partenaires
- **Auth Required**: Oui (JWT Token)
- **Query Parameters**:
  - type: Type d'établissement (RESTAURANT, HOTEL, PRIVATE_BEACH)
  - services: Services recherchés (ex: "Navettes maritimes")
- **Response (200 OK)**:
```json
[
  {
    "id": 3,
    "name": "Hôtel Riviera",
    "type": "HOTEL",
    "address": "123 Boulevard de la Mer, 83990 Saint-Tropez",
    "main_photo": "https://example.com/establishments/hotel_riviera_main.jpg",
    "average_rating": 4.6,
    "services_offered": ["Restaurant", "Spa", "Plage privée", "Navettes maritimes"]
  },
  // ...
]
```

## 2. Gestion de la disponibilité (pour les capitaines)

### 2.1. Mettre à jour la disponibilité
- **Endpoint**: POST /api/accounts/captains/availability/
- **Description**: Mettre à jour le statut de disponibilité d'un capitaine
- **Auth Required**: Oui (JWT Token)
- **Request Body**:
```json
{
  "availability_status": "UNAVAILABLE",
  "reason": "Congés",
  "start_date": "2025-06-01",
  "end_date": "2025-06-15"
}
```
- **Response (200 OK)**:
```json
{
  "message": "Disponibilité mise à jour avec succès"
}
```

### 2.2. Récupérer le calendrier de disponibilité
- **Endpoint**: GET /api/accounts/captains/availability/calendar/
- **Description**: Récupérer le calendrier de disponibilité d'un capitaine
- **Auth Required**: Oui (JWT Token)
- **Query Parameters**:
  - year: Année (ex: 2025)
  - month: Mois (ex: 6)
- **Response (200 OK)**:
```json
{
  "captain_id": 5,
  "year": 2025,
  "month": 6,
  "days": [
    {
      "date": "2025-06-01",
      "status": "UNAVAILABLE",
      "reason": "Congés"
    },
    // ...
    {
      "date": "2025-06-15",
      "status": "UNAVAILABLE",
      "reason": "Congés"
    },
    {
      "date": "2025-06-16",
      "status": "AVAILABLE",
      "trips": [
        {
          "id": 125,
          "start_time": "2025-06-16T10:00:00Z",
          "end_time": "2025-06-16T12:00:00Z"
        }
      ]
    }
  ]
}
```

## 3. Favoris et préférences

### 3.1. Ajouter un capitaine aux favoris
- **Endpoint**: POST /api/accounts/favorites/captains/
- **Description**: Ajouter un capitaine à la liste des favoris
- **Auth Required**: Oui (JWT Token)
- **Request Body**:
```json
{
  "captain": 5,
  "notes": "Mon capitaine préféré pour les sorties en mer"
}
```
- **Response (201 Created)**:
```json
{
  "id": 1,
  "captain": 5,
  "captain_details": {
    "user": {
      "id": 7,
      "email": "<EMAIL>",
      "first_name": "Jean",
      "last_name": "Dupont",
      "phone_number": "+***********"
    },
    "experience": "15 ans de navigation en Méditerranée",
    "average_rating": 4.8,
    "total_trips": 237,
    "is_available": true
  },
  "notes": "Mon capitaine préféré pour les sorties en mer",
  "created_at": "2025-05-25T12:30:45Z"
}
```

### 3.2. Supprimer un capitaine des favoris
- **Endpoint**: DELETE /api/accounts/favorites/captains/{id}/
- **Description**: Supprimer un capitaine de la liste des favoris
- **Auth Required**: Oui (JWT Token)
- **Response (204 No Content)**

### 3.3. Détails d'un capitaine favori
- **Endpoint**: GET /api/accounts/favorites/captains/{id}/
- **Description**: Récupérer les détails d'un capitaine favori spécifique
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
{
  "id": 1,
  "captain": 5,
  "captain_details": {
    "user": {
      "id": 7,
      "email": "<EMAIL>",
      "first_name": "Jean",
      "last_name": "Dupont",
      "phone_number": "+***********"
    },
    "experience": "15 ans de navigation en Méditerranée",
    "average_rating": 4.8,
    "total_trips": 237,
    "is_available": true
  },
  "notes": "Mon capitaine préféré pour les sorties en mer",
  "created_at": "2025-05-25T12:30:45Z"
}
```

### 3.4. Modifier les notes d'un capitaine favori
- **Endpoint**: PATCH /api/accounts/favorites/captains/{id}/
- **Description**: Mettre à jour les notes associées à un capitaine favori
- **Auth Required**: Oui (JWT Token)
- **Request Body**:
```json
{
  "notes": "Excellent pour les sorties nocturnes"
}
```
- **Response (200 OK)**:
```json
{
  "id": 1,
  "captain": 5,
  "captain_details": {
    "user": {
      "id": 7,
      "email": "<EMAIL>",
      "first_name": "Jean",
      "last_name": "Dupont",
      "phone_number": "+***********"
    },
    "experience": "15 ans de navigation en Méditerranée",
    "average_rating": 4.8,
    "total_trips": 237,
    "is_available": true
  },
  "notes": "Excellent pour les sorties nocturnes",
  "created_at": "2025-05-25T12:30:45Z"
}
```

### 3.5. Liste des capitaines favoris
- **Endpoint**: GET /api/accounts/favorites/captains/
- **Description**: Récupérer la liste des capitaines favoris
- **Auth Required**: Oui (JWT Token)
- **Response (200 OK)**:
```json
[
  {
    "id": 1,
    "captain": 5,
    "captain_details": {
      "user": {
        "id": 7,
        "email": "<EMAIL>",
        "first_name": "Jean",
        "last_name": "Dupont",
        "phone_number": "+***********"
      },
      "experience": "15 ans de navigation en Méditerranée",
      "average_rating": 4.8,
      "total_trips": 237,
      "is_available": true
    },
    "notes": "Mon capitaine préféré pour les sorties en mer",
    "created_at": "2025-05-25T12:30:45Z"
  },
  {
    "id": 2,
    "captain": 8,
    "captain_details": {
      "user": {
        "id": 9,
        "email": "<EMAIL>",
        "first_name": "Sophie",
        "last_name": "Martin",
        "phone_number": "+***********"
      },
      "experience": "10 ans d'expérience dans les îles d'Or",
      "average_rating": 4.9,
      "total_trips": 189,
      "is_available": true
    },
    "notes": "Excellente pour les excursions aux îles",
    "created_at": "2025-05-20T09:15:30Z"
  }
]
```

### 3.6. Ajouter un emplacement favori
- **Endpoint**: POST /api/accounts/favorites/locations/
- **Description**: Ajouter un emplacement à la liste des favoris
- **Auth Required**: Oui (JWT Token)
- **Request Body**:
```json
{
  "name": "Domicile",
  "address": "23 rue de la Mer, 83990 Saint-Tropez",
  "coordinates": "43.2721,6.6410",
  "notes": "Facilement accessible par la route côtière"
}
```
- **Response (201 Created)**:
```json
{
  "id": 1,
  "name": "Domicile",
  "address": "23 rue de la Mer, 83990 Saint-Tropez",
  "coordinates": "43.2721,6.6410",
  "notes": "Facilement accessible par la route côtière",
  "created_at": "2025-05-25T13:05:23Z"
}
```

## Fonctionnalités implémentées

1. ✅ **Implémentation des vues** : Les vues API basées sur les endpoints documentés ont été créées et sont opérationnelles.

2. ✅ **Autorisations** : Les permissions ont été configurées pour s'assurer que seuls les utilisateurs authentifiés peuvent accéder aux informations.

3. ✅ **Système de favoris** : La gestion complète des favoris (capitaines, emplacements) a été implémentée.

4. ✅ **Notifications** : Intégration avec le système de notifications pour informer les utilisateurs des changements de statut.

## Fonctionnalités à implémenter

1. **Validation des profils** : Mettre en place un système de validation des profils capitaines pour garantir la qualité du service.

2. **Gestion des documents** : Permettre le téléchargement et la vérification des documents (permis, certifications, etc.).

3. **Système de réputation avancé** : Développer un algorithme plus sophistiqué de calcul de la réputation basé sur les avis, le nombre de courses, etc.
