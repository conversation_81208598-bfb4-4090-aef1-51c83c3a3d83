"""
Vues pour la gestion des navettes par les établissements
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.utils import timezone
from datetime import datetime, timedelta
from .models import Trip, TripRequest, ShuttleTripRequest
from .serializers import TripSerializer, ShuttleTripRequestSerializer
from boats.models import Boat
from accounts.models import Captain


class ShuttleAcceptView(APIView):
    """Accepter une demande de navette (établissement)"""
    permission_classes = [IsAuthenticated]

    def patch(self, request, request_id):
        shuttle_request = get_object_or_404(ShuttleTripRequest, id=request_id)
        
        # Vérifier que l'utilisateur est de l'établissement concerné
        if not hasattr(request.user, 'establishment') or shuttle_request.establishment != request.user.establishment:
            return Response(
                {'error': 'Seul l\'établissement concerné peut accepter cette demande'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Vérifier que la demande est en attente
        if shuttle_request.status != TripRequest.Status.PENDING:
            return Response(
                {'error': f'Cette demande ne peut pas être acceptée. Statut: {shuttle_request.status}'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Récupérer les données optionnelles
        assigned_boat_id = request.data.get('boat_id')
        assigned_captain_id = request.data.get('captain_id')
        pickup_time = request.data.get('pickup_time')  # Heure de ramassage confirmée
        notes = request.data.get('notes', '')
        
        # Attribution automatique si pas spécifié
        boat, captain = self._assign_boat_and_captain(
            shuttle_request.establishment, 
            assigned_boat_id, 
            assigned_captain_id,
            shuttle_request.departure_date,
            shuttle_request.departure_time
        )
        
        if not boat or not captain:
            return Response(
                {'error': 'Aucun bateau ou capitaine disponible pour cette navette'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Créer la course officielle
        trip_data = {
            'client': shuttle_request.client,
            'captain': captain,
            'boat': boat,
            'establishment': shuttle_request.establishment,
            'start_location': shuttle_request.departure_location.get('city_name', ''),
            'end_location': shuttle_request.establishment.address,
            'passenger_count': shuttle_request.passenger_count,
            'base_price': 0,  # Navette gratuite
            'total_price': 0,
            'status': Trip.Status.ACCEPTED,
            'payment_status': 'COMPLETED',  # Gratuit
            'payment_method': 'FREE_SHUTTLE'
        }
        
        # Définir les heures
        if pickup_time:
            # Utiliser l'heure confirmée par l'établissement
            pickup_datetime = datetime.combine(shuttle_request.departure_date, 
                                             datetime.strptime(pickup_time, '%H:%M').time())
        else:
            # Utiliser l'heure demandée
            pickup_datetime = datetime.combine(shuttle_request.departure_date, 
                                             shuttle_request.departure_time)
        
        trip_data['scheduled_start_time'] = timezone.make_aware(pickup_datetime)
        trip_data['scheduled_end_time'] = trip_data['scheduled_start_time'] + timedelta(hours=1)
        
        # Créer la course
        trip = Trip.objects.create(**trip_data)
        
        # Mettre à jour la demande
        shuttle_request.status = TripRequest.Status.ACCEPTED
        shuttle_request.save()
        
        # Ajouter des notes si fournies
        if notes:
            trip.notes = f"Navette acceptée par {request.user.establishment.business_name}: {notes}"
            trip.save()
        
        return Response({
            'message': 'Navette acceptée et course créée',
            'trip_id': trip.id,
            'trip': TripSerializer(trip).data,
            'pickup_time': trip.scheduled_start_time,
            'boat': boat.name,
            'captain': f"{captain.user.first_name} {captain.user.last_name}"
        }, status=status.HTTP_201_CREATED)
    
    def _assign_boat_and_captain(self, establishment, boat_id=None, captain_id=None, date=None, time=None):
        """Assigne automatiquement un bateau et un capitaine"""
        
        # Si spécifiés, utiliser les IDs fournis
        if boat_id and captain_id:
            try:
                boat = Boat.objects.get(id=boat_id, establishment=establishment, is_available=True)
                captain = Captain.objects.get(id=captain_id, is_available=True)
                return boat, captain
            except:
                pass
        
        # Attribution automatique
        # Chercher les bateaux disponibles de l'établissement
        available_boats = Boat.objects.filter(
            establishment=establishment,
            is_available=True
        )
        
        if not available_boats.exists():
            return None, None
        
        # Prendre le premier bateau disponible
        boat = available_boats.first()
        
        # Chercher un capitaine disponible
        # Pour les navettes, on peut utiliser n'importe quel capitaine disponible
        available_captains = Captain.objects.filter(
            is_available=True,
            availability_status='AVAILABLE'
        )
        
        if not available_captains.exists():
            return None, None
        
        captain = available_captains.first()
        
        return boat, captain


class ShuttleRejectView(APIView):
    """Refuser une demande de navette (établissement)"""
    permission_classes = [IsAuthenticated]

    def patch(self, request, request_id):
        shuttle_request = get_object_or_404(ShuttleTripRequest, id=request_id)
        
        # Vérifier que l'utilisateur est de l'établissement concerné
        if not hasattr(request.user, 'establishment') or shuttle_request.establishment != request.user.establishment:
            return Response(
                {'error': 'Seul l\'établissement concerné peut refuser cette demande'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Vérifier que la demande est en attente
        if shuttle_request.status != TripRequest.Status.PENDING:
            return Response(
                {'error': f'Cette demande ne peut pas être refusée. Statut: {shuttle_request.status}'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Récupérer la raison du refus
        rejection_reason = request.data.get('reason', '')
        if not rejection_reason:
            return Response(
                {'error': 'Une raison de refus est requise'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Mettre à jour la demande
        shuttle_request.status = TripRequest.Status.REJECTED
        shuttle_request.message += f"\n[REFUSÉ] {rejection_reason}"
        shuttle_request.save()
        
        return Response({
            'message': 'Demande de navette refusée',
            'reason': rejection_reason
        })


class ShuttleListView(APIView):
    """Lister les demandes de navettes pour un établissement"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        if not hasattr(request.user, 'establishment'):
            return Response(
                {'error': 'Seuls les établissements peuvent voir les demandes de navettes'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Filtres optionnels
        status_filter = request.query_params.get('status', 'PENDING')
        date_from = request.query_params.get('date_from')
        date_to = request.query_params.get('date_to')
        
        # Récupérer les demandes
        queryset = ShuttleTripRequest.objects.filter(
            establishment=request.user.establishment
        )
        
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        if date_from:
            queryset = queryset.filter(departure_date__gte=date_from)
        
        if date_to:
            queryset = queryset.filter(departure_date__lte=date_to)
        
        queryset = queryset.order_by('-created_at')
        
        serializer = ShuttleTripRequestSerializer(queryset, many=True)
        
        return Response({
            'count': queryset.count(),
            'requests': serializer.data
        })


class ShuttleAvailableResourcesView(APIView):
    """Lister les bateaux et capitaines disponibles pour les navettes"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        if not hasattr(request.user, 'establishment'):
            return Response(
                {'error': 'Accès refusé'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Bateaux de l'établissement
        boats = Boat.objects.filter(
            establishment=request.user.establishment,
            is_available=True
        )
        
        # Capitaines disponibles
        captains = Captain.objects.filter(
            is_available=True,
            availability_status='AVAILABLE'
        )
        
        boats_data = [
            {
                'id': boat.id,
                'name': boat.name,
                'capacity': boat.capacity,
                'boat_type': boat.boat_type,
                'registration_number': boat.registration_number
            }
            for boat in boats
        ]
        
        captains_data = [
            {
                'id': captain.id,
                'name': f"{captain.user.first_name} {captain.user.last_name}",
                'experience': captain.experience,
                'license_number': captain.license_number,
                'average_rating': captain.average_rating
            }
            for captain in captains
        ]
        
        return Response({
            'available_boats': boats_data,
            'available_captains': captains_data
        })


class ShuttleScheduleView(APIView):
    """Gérer le planning des navettes d'un établissement"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        if not hasattr(request.user, 'establishment'):
            return Response(
                {'error': 'Accès refusé'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Récupérer les navettes programmées
        date_from = request.query_params.get('date_from', timezone.now().date())
        date_to = request.query_params.get('date_to', timezone.now().date() + timedelta(days=7))
        
        # Navettes acceptées
        accepted_shuttles = Trip.objects.filter(
            establishment=request.user.establishment,
            payment_method='FREE_SHUTTLE',
            scheduled_start_time__date__range=[date_from, date_to]
        ).order_by('scheduled_start_time')
        
        schedule_data = [
            {
                'trip_id': trip.id,
                'client_name': f"{trip.client.user.first_name} {trip.client.user.last_name}",
                'pickup_time': trip.scheduled_start_time,
                'pickup_location': trip.start_location,
                'passenger_count': trip.passenger_count,
                'boat': trip.boat.name if trip.boat else None,
                'captain': f"{trip.captain.user.first_name} {trip.captain.user.last_name}",
                'status': trip.status
            }
            for trip in accepted_shuttles
        ]
        
        return Response({
            'date_range': f"{date_from} to {date_to}",
            'total_shuttles': len(schedule_data),
            'schedule': schedule_data
        })
