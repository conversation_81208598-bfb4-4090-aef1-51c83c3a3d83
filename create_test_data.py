import os
import django
import datetime
from decimal import Decimal

# Configurer l'environnement Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from django.utils import timezone
from django.contrib.auth import get_user_model
from accounts.models import C<PERSON>, Captain, Establishment
from boats.models import Boat, MaintenanceRecord
from trips.models import Trip, Shuttle
from payments.models import Wallet, Transaction

User = get_user_model()

def create_test_shuttle():
    """Crée une navette de test avec l'ID 5"""
    try:
        # Vérifier si la navette existe déjà
        if Shuttle.objects.filter(id=5).exists():
            print("La navette avec l'ID 5 existe déjà.")
            return Shuttle.objects.get(id=5)

        # Récupérer ou créer un établissement
        try:
            establishment = Establishment.objects.get(user_id=3)
        except Establishment.DoesNotExist:
            # Créer l'utilisateur si nécessaire
            try:
                user = User.objects.get(id=3)
            except User.DoesNotExist:
                user = User.objects.create_user(
                    id=3,
                    email='<EMAIL>',
                    password='test123',
                    type='ESTABLISHMENT'
                )
            
            establishment = Establishment.objects.create(
                user=user,
                name='Établissement Test',
                type='RESTAURANT',
                address='123 Rue de Test',
                description='Un établissement pour les tests',
                website='https://example.com'
            )

        # Récupérer ou créer un capitaine
        try:
            captain = Captain.objects.get(user_id=4)
        except Captain.DoesNotExist:
            # Créer l'utilisateur si nécessaire
            try:
                user = User.objects.get(id=4)
            except User.DoesNotExist:
                user = User.objects.create_user(
                    id=4,
                    email='<EMAIL>',
                    password='test123',
                    type='CAPTAIN'
                )
            
            captain = Captain.objects.create(
                user=user,
                experience='Capitaine expérimenté',
                license_number='LIC12345',
                years_of_experience=5
            )

        # Récupérer ou créer un bateau
        boat, _ = Boat.objects.get_or_create(
            id=5,
            defaults={
                'name': 'Bateau Test',
                'registration_number': 'TEST-123',
                'color': 'Bleu',
                'capacity': 10,
                'fuel_type': 'DIESEL',
                'fuel_consumption': Decimal('5.5'),
                'captain': captain,
                'is_available': True
            }
        )

        # Créer la navette avec l'ID spécifique
        shuttle = Shuttle(
            id=5,
            establishment=establishment,
            boat=boat,
            captain=captain,
            route_name='Route Test',
            start_location='Port de Test',
            end_location='Destination Test',
            departure_time=timezone.now() + datetime.timedelta(days=1),
            arrival_time=timezone.now() + datetime.timedelta(days=1, hours=2),
            max_capacity=8,
            price_per_person=Decimal('50.00'),
            status='SCHEDULED'
        )
        shuttle.save()
        print(f"Navette créée avec l'ID {shuttle.id}")
        return shuttle
    except Exception as e:
        print(f"Erreur lors de la création de la navette: {str(e)}")
        return None

def create_test_maintenance():
    """Crée un enregistrement de maintenance de test avec l'ID 3"""
    try:
        # Vérifier si la maintenance existe déjà
        if MaintenanceRecord.objects.filter(id=3).exists():
            print("La maintenance avec l'ID 3 existe déjà.")
            return MaintenanceRecord.objects.get(id=3)

        # Récupérer un bateau existant ou en créer un
        boat = None
        try:
            boat = Boat.objects.get(id=5)
        except Boat.DoesNotExist:
            # Récupérer ou créer un capitaine
            captain, _ = Captain.objects.get_or_create(
                user=User.objects.get(id=3),
                defaults={
                    'phone': '+33612345678',
                    'bio': 'Capitaine de test',
                    'experience_years': 5,
                    'license_number': 'LIC12345'
                }
            )
            
            boat = Boat.objects.create(
                id=5,
                name='Bateau Test',
                registration_number='TEST-123',
                color='Bleu',
                capacity=10,
                fuel_type='DIESEL',
                fuel_consumption=Decimal('5.5'),
                captain=captain,
                is_available=True
            )

        # Créer l'enregistrement de maintenance avec l'ID spécifique
        maintenance = MaintenanceRecord(
            id=3,
            boat=boat,
            maintenance_type='ROUTINE',
            description='Maintenance de test',
            cost=Decimal('250.00'),
            performed_at=timezone.now(),
            performed_by='Technicien Test'
        )
        maintenance.save()
        print(f"Maintenance créée avec l'ID {maintenance.id}")
        return maintenance
    except Exception as e:
        print(f"Erreur lors de la création de la maintenance: {str(e)}")
        return None

def add_shared_payment_token_to_script():
    """Ajoute un token pour le paiement partagé dans le script de test"""
    try:
        # Générer un token fictif
        token = 'TEST_TOKEN_123456789'
        
        # Chemin du fichier script
        script_path = 'd:\\commodore\\scripts\\test_payments.ps1'
        
        # Lire le contenu du fichier
        with open(script_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # Remplacer le token dans le script
        if '$SHARED_PAYMENT_TOKEN' in content:
            content = content.replace('$SHARED_PAYMENT_TOKEN = ""', f'$SHARED_PAYMENT_TOKEN = "{token}"')
            
            # Écrire le contenu modifié dans le fichier
            with open(script_path, 'w', encoding='utf-8') as file:
                file.write(content)
            
            print(f"Token de paiement partagé ajouté au script: {token}")
        else:
            # Ajouter la variable juste après la déclaration du token JWT
            token_line = '$TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ4Mjk2OTg0LCJpYXQiOjE3NDgyMTA1ODQsImp0aSI6IjJfMTc0ODIxMDU4NC4zNzUyMTMiLCJ1c2VyX2lkIjoyfQ.020cRZGNI8czl5DnOnETaDGlfyV2pl0I-B_dB9C12aA"'
            new_token_line = f'\n# Token pour le test de paiement partagé\n$SHARED_PAYMENT_TOKEN = "{token}"'
            
            content = content.replace(token_line, token_line + new_token_line)
            
            # Écrire le contenu modifié dans le fichier
            with open(script_path, 'w', encoding='utf-8') as file:
                file.write(content)
            
            print(f"Variable de token de paiement partagé ajoutée au script: {token}")
            
        return token
    except Exception as e:
        print(f"Erreur lors de l'ajout du token au script: {str(e)}")
        return None

def add_settings_to_project():
    """Ajoute les paramètres manquants au fichier settings.py"""
    try:
        # Chemin du fichier settings
        settings_path = 'd:\\commodore\\commodore\\settings.py'
        
        # Lire le contenu du fichier
        with open(settings_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # Ajouter les paramètres manquants s'ils n'existent pas déjà
        new_settings = "\n# Paramètres pour les paiements\nPAYMENT_RETURN_URL = 'http://localhost:8000/payment/success'\nPAYMENT_CANCEL_URL = 'http://localhost:8000/payment/cancel'\n"
        
        if 'PAYMENT_RETURN_URL' not in content:
            # Ajouter les paramètres à la fin du fichier
            with open(settings_path, 'a', encoding='utf-8') as file:
                file.write(new_settings)
            
            print("Paramètres ajoutés au fichier settings.py")
        else:
            print("Les paramètres existent déjà dans le fichier settings.py")
            
        return True
    except Exception as e:
        print(f"Erreur lors de l'ajout des paramètres au fichier settings.py: {str(e)}")
        return False

if __name__ == '__main__':
    create_test_shuttle()
    create_test_maintenance()
    add_shared_payment_token_to_script()
    add_settings_to_project()
