from django.contrib import admin
from .models import User, Client, Captain, Establishment
from .favorites import FavoriteLocation, FavoriteCaptain

@admin.register(User)
class UserAdmin(admin.ModelAdmin):
    list_display = ('id', 'email', 'type', 'is_active', 'email_verified', 'phone_number', 'date_joined')
    search_fields = ('email', 'phone_number')
    list_filter = ('type', 'is_active', 'email_verified')
    ordering = ('-date_joined',)

@admin.register(Client)
class ClientAdmin(admin.ModelAdmin):
    list_display = ('pk', 'user', 'wallet_balance', 'date_of_birth', 'nationality', 'preferred_language')
    search_fields = ('user__email', 'nationality')
    list_filter = ('preferred_language',)

@admin.register(Captain)
class CaptainAdmin(admin.ModelAdmin):
    list_display = ('pk', 'user', 'experience', 'average_rating', 'total_trips', 'is_available')
    search_fields = ('user__email',)
    list_filter = ('is_available', 'average_rating')

@admin.register(Establishment)
class EstablishmentAdmin(admin.ModelAdmin):
    list_display = ('pk', 'user', 'name', 'type', 'business_name', 'average_rating')
    search_fields = ('user__email', 'name', 'business_name')
    list_filter = ('type', 'business_type', 'average_rating')

@admin.register(FavoriteLocation)
class FavoriteLocationAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'name', 'address', 'coordinates', 'created_at')
    search_fields = ('user__email', 'name', 'address')
    list_filter = ('created_at',)

@admin.register(FavoriteCaptain)
class FavoriteCaptainAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'captain', 'created_at')
    search_fields = ('user__email', 'captain__user__email')
    list_filter = ('created_at',)
