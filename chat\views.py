from rest_framework import viewsets, status, mixins
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db.models import Q

from .models import ChatRoom, Message, ChatbotSession, ChatbotMessage
from .serializers import (
    ChatRoomSerializer, ChatRoomListSerializer, MessageSerializer,
    ChatbotSessionSerializer, ChatbotMessageSerializer
)

class ChatRoomViewSet(viewsets.ModelViewSet):
    serializer_class = ChatRoomSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return ChatRoom.objects.filter(
            participants=self.request.user,
            is_active=True
        ).order_by('-updated_at')

    def get_serializer_class(self):
        if self.action == 'list':
            return ChatRoomListSerializer
        return ChatRoomSerializer

    @action(detail=True, methods=['post'])
    def mark_as_read(self, request, pk=None):
        room = self.get_object()
        messages = room.messages.filter(
            is_read=False
        ).exclude(sender=request.user)

        for message in messages:
            message.is_read = True
            message.read_by.add(request.user)
            message.save()

        return Response({'status': 'messages marked as read'})

class MessageViewSet(viewsets.ModelViewSet):
    serializer_class = MessageSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        room_id = self.kwargs.get('room_pk')
        return Message.objects.filter(
            room_id=room_id,
            room__participants=self.request.user,
            deleted_at__isnull=True
        ).order_by('created_at')

    def perform_create(self, serializer):
        room = get_object_or_404(
            ChatRoom,
            id=self.kwargs.get('room_pk'),
            participants=self.request.user
        )
        serializer.save(room=room, sender=self.request.user)

    @action(detail=True, methods=['post'])
    def mark_as_read(self, request, room_pk=None, pk=None):
        message = self.get_object()
        if message.sender != request.user:
            message.is_read = True
            message.read_by.add(request.user)
            message.save()
        return Response({'status': 'message marked as read'})

    @action(detail=True, methods=['post'])
    def soft_delete(self, request, room_pk=None, pk=None):
        message = self.get_object()
        if message.sender == request.user:
            message.deleted_at = timezone.now()
            message.save()
            return Response({'status': 'message deleted'})
        return Response(
            {'error': 'You can only delete your own messages'},
            status=status.HTTP_403_FORBIDDEN
        )

class ChatbotSessionViewSet(viewsets.ModelViewSet):
    serializer_class = ChatbotSessionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return ChatbotSession.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

class ChatbotMessageViewSet(viewsets.ModelViewSet):
    serializer_class = ChatbotMessageSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        session_id = self.kwargs.get('session_pk')
        return ChatbotMessage.objects.filter(
            session_id=session_id,
            session__user=self.request.user
        ).order_by('created_at')

    def perform_create(self, serializer):
        session = get_object_or_404(
            ChatbotSession,
            id=self.kwargs.get('session_pk'),
            user=self.request.user
        )
        serializer.save(session=session)
