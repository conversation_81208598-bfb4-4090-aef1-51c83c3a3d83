from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from .models import (
    Trip, Shuttle, TripRequest, SimpleTripRequest,
    HourlyTripRequest, ShuttleTripRequest, TripQuote
)
from .serializers import (
    TripSerializer, TripListSerializer,
    ShuttleSerializer, ShuttleListSerializer,
    SimpleTripRequestSerializer, HourlyTripRequestSerializer,
    ShuttleTripRequestSerializer, TripQuoteSerializer
)
from accounts.models import Client, Captain, Establishment
from boats.models import Boat
from django.db.models import Q, F
from datetime import datetime, timedelta
from django.utils import timezone
from decimal import Decimal

class TripListCreateView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        # Filtrer selon le type d'utilisateur
        if hasattr(request.user, 'client'):
            trips = Trip.objects.filter(client=request.user.client)
        elif hasattr(request.user, 'captain'):
            trips = Trip.objects.filter(captain=request.user.captain)
        elif hasattr(request.user, 'establishment'):
            trips = Trip.objects.filter(establishment=request.user.establishment)
        else:
            return Response(
                {'error': 'Type d\'utilisateur non autorisé'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Appliquer les filtres
        status_filter = request.query_params.get('status')
        date_from = request.query_params.get('date_from')
        date_to = request.query_params.get('date_to')

        if status_filter:
            trips = trips.filter(status=status_filter)
        if date_from:
            trips = trips.filter(scheduled_start_time__date__gte=date_from)
        if date_to:
            trips = trips.filter(scheduled_start_time__date__lte=date_to)

        serializer = TripListSerializer(trips, many=True)
        return Response(serializer.data)

    def post(self, request):
        if not hasattr(request.user, 'client'):
            return Response(
                {'error': 'Seuls les clients peuvent créer des courses'},
                status=status.HTTP_403_FORBIDDEN
            )

        data = request.data.copy()
        data['client'] = request.user.client.pk
        data['status'] = 'PENDING'

        # Vérifier la disponibilité du bateau
        boat = get_object_or_404(Boat, pk=data.get('boat'))
        if boat.status != 'AVAILABLE':
            return Response(
                {'error': 'Ce bateau n\'est pas disponible'},
                status=status.HTTP_400_BAD_REQUEST
            )

        serializer = TripSerializer(data=data)
        if serializer.is_valid():
            trip = serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class TripDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, pk):
        trip = get_object_or_404(Trip, pk=pk)

        # Vérifier les permissions
        if not self._has_permission(request.user, trip):
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = TripSerializer(trip)
        return Response(serializer.data)

    def patch(self, request, pk):
        trip = get_object_or_404(Trip, pk=pk)

        if not self._has_permission(request.user, trip):
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Vérifier les transitions de statut autorisées
        new_status = request.data.get('status')
        if new_status and not self._is_valid_status_transition(trip, new_status, request.user):
            return Response(
                {'error': 'Transition de statut non autorisée'},
                status=status.HTTP_400_BAD_REQUEST
            )

        serializer = TripSerializer(trip, data=request.data, partial=True)
        if serializer.is_valid():
            trip = serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        trip = get_object_or_404(Trip, pk=pk)

        if not self._has_permission(request.user, trip):
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )

        if trip.status not in ['PENDING', 'SCHEDULED']:
            return Response(
                {'error': 'Impossible d\'annuler une course en cours ou terminée'},
                status=status.HTTP_400_BAD_REQUEST
            )

        trip.status = 'CANCELLED'
        trip.save()
        return Response(status=status.HTTP_204_NO_CONTENT)

    def _has_permission(self, user, trip):
        return (
            hasattr(user, 'client') and trip.client == user.client or
            hasattr(user, 'captain') and trip.captain == user.captain or
            hasattr(user, 'establishment') and trip.establishment == user.establishment
        )

    def _is_valid_status_transition(self, trip, new_status, user):
        valid_transitions = {
            'PENDING': {
                'client': ['CANCELLED'],
                'captain': ['ACCEPTED', 'REJECTED'],
                'establishment': ['ACCEPTED', 'REJECTED']
            },
            'ACCEPTED': {
                'captain': ['IN_PROGRESS', 'CANCELLED'],
                'establishment': ['IN_PROGRESS', 'CANCELLED']
            },
            'IN_PROGRESS': {
                'captain': ['COMPLETED'],
                'establishment': ['COMPLETED']
            }
        }

        user_type = 'client' if hasattr(user, 'client') else \
                   'captain' if hasattr(user, 'captain') else \
                   'establishment' if hasattr(user, 'establishment') else None

        return (
            trip.status in valid_transitions and
            user_type in valid_transitions[trip.status] and
            new_status in valid_transitions[trip.status][user_type]
        )

class TripTrackingView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, pk):
        trip = get_object_or_404(Trip, pk=pk)

        if not (
            hasattr(request.user, 'client') and trip.client == request.user.client or
            hasattr(request.user, 'captain') and trip.captain == request.user.captain or
            hasattr(request.user, 'establishment') and trip.establishment == request.user.establishment
        ):
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )

        return Response({
            'trip_id': trip.pk,
            'status': trip.status,
            'current_location': trip.current_location,
            'tracking_data': trip.tracking_data,
            'estimated_arrival_time': trip.estimated_arrival_time
        })

    def post(self, request, pk):
        trip = get_object_or_404(Trip, pk=pk)

        if not (hasattr(request.user, 'captain') and trip.captain == request.user.captain):
            return Response(
                {'error': 'Seul le capitaine peut mettre à jour la position'},
                status=status.HTTP_403_FORBIDDEN
            )

        if trip.status != 'IN_PROGRESS':
            return Response(
                {'error': 'La course doit être en cours pour mettre à jour la position'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Mettre à jour la position et les données de suivi
        trip.current_location = request.data.get('current_location')
        trip.tracking_data = request.data.get('tracking_data')
        trip.estimated_arrival_time = request.data.get('estimated_arrival_time')
        trip.save()

        return Response({
            'message': 'Position mise à jour avec succès',
            'current_location': trip.current_location,
            'estimated_arrival_time': trip.estimated_arrival_time
        })

class ShuttleListCreateView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        if hasattr(request.user, 'establishment'):
            shuttles = Shuttle.objects.filter(establishment=request.user.establishment)
        elif hasattr(request.user, 'captain'):
            shuttles = Shuttle.objects.filter(captain=request.user.captain)
        else:
            shuttles = Shuttle.objects.filter(status='SCHEDULED')

        # Appliquer les filtres
        date = request.query_params.get('date')
        route = request.query_params.get('route')
        available_seats = request.query_params.get('available_seats')

        if date:
            shuttles = shuttles.filter(departure_time__date=date)
        if route:
            shuttles = shuttles.filter(
                Q(departure_location__icontains=route) |
                Q(arrival_location__icontains=route)
            )
        if available_seats:
            shuttles = shuttles.filter(
                max_capacity__gte=models.F('current_bookings') + int(available_seats)
            )

        serializer = ShuttleListSerializer(shuttles, many=True)
        return Response(serializer.data)

    def post(self, request):
        if not hasattr(request.user, 'establishment'):
            return Response(
                {'error': 'Seuls les établissements peuvent créer des navettes'},
                status=status.HTTP_403_FORBIDDEN
            )

        data = request.data.copy()
        data['establishment'] = request.user.establishment.pk
        data['status'] = 'SCHEDULED'

        serializer = ShuttleSerializer(data=data)
        if serializer.is_valid():
            shuttle = serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class ShuttleDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, pk):
        shuttle = get_object_or_404(Shuttle, pk=pk)
        serializer = ShuttleSerializer(shuttle)
        return Response(serializer.data)

    def patch(self, request, pk):
        shuttle = get_object_or_404(Shuttle, pk=pk)

        if not (
            hasattr(request.user, 'establishment') and
            shuttle.establishment == request.user.establishment
        ):
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = ShuttleSerializer(shuttle, data=request.data, partial=True)
        if serializer.is_valid():
            shuttle = serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        shuttle = get_object_or_404(Shuttle, pk=pk)

        if not (
            hasattr(request.user, 'establishment') and
            shuttle.establishment == request.user.establishment
        ):
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )

        if shuttle.status not in ['SCHEDULED']:
            return Response(
                {'error': 'Impossible d\'annuler une navette en cours ou terminée'},
                status=status.HTTP_400_BAD_REQUEST
            )

        shuttle.status = 'CANCELLED'
        shuttle.save()
        return Response(status=status.HTTP_204_NO_CONTENT)


# Nouvelles vues pour les trois types de courses

class SimpleTripRequestView(APIView):
    """Gestion des courses simples"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Créer une demande de course simple et retourner les devis"""
        if not hasattr(request.user, 'client'):
            return Response(
                {'error': 'Seuls les clients peuvent créer des demandes de courses'},
                status=status.HTTP_403_FORBIDDEN
            )

        data = request.data.copy()
        data['client'] = request.user.client.pk

        serializer = SimpleTripRequestSerializer(data=data)
        if serializer.is_valid():
            trip_request = serializer.save()

            # Calculer la distance
            trip_request.calculate_distance()

            # Générer les devis automatiquement
            quotes = self._generate_quotes(trip_request)

            return Response({
                'trip_request': SimpleTripRequestSerializer(trip_request).data,
                'quotes': quotes
            }, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def _generate_quotes(self, trip_request):
        """Générer les devis pour une course simple"""
        quotes = []

        # Récupérer tous les capitaines disponibles avec le bon type de bateau
        available_captains = Captain.objects.filter(
            is_available=True,
            boat__boat_type=trip_request.boat_type,
            boat__is_available=True,
            boat__capacity__gte=trip_request.passenger_count
        ).select_related('user', 'boat')

        for captain in available_captains:
            if captain.rate_per_km and trip_request.distance_km:
                # Calcul pour course simple (par kilomètre)
                base_price = trip_request.distance_km * captain.rate_per_km

                # Créer le devis
                quote_data = {
                    'trip_request': trip_request,
                    'captain': captain,
                    'boat': captain.boat,
                    'base_price': base_price,
                    'distance_km': trip_request.distance_km,
                    'rate_used': captain.rate_per_km,
                }

                quote = TripQuote.objects.create(**quote_data)
                quotes.append(TripQuoteSerializer(quote).data)

        return quotes


class HourlyTripRequestView(APIView):
    """Gestion des mises à disposition"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Créer une demande de mise à disposition et retourner les devis"""
        if not hasattr(request.user, 'client'):
            return Response(
                {'error': 'Seuls les clients peuvent créer des demandes de courses'},
                status=status.HTTP_403_FORBIDDEN
            )

        data = request.data.copy()
        data['client'] = request.user.client.pk

        serializer = HourlyTripRequestSerializer(data=data)
        if serializer.is_valid():
            trip_request = serializer.save()

            # Calculer la distance
            trip_request.calculate_distance()

            # Générer les devis automatiquement
            quotes = self._generate_quotes(trip_request)

            return Response({
                'trip_request': HourlyTripRequestSerializer(trip_request).data,
                'quotes': quotes
            }, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def _generate_quotes(self, trip_request):
        """Générer les devis pour une mise à disposition"""
        quotes = []

        # Récupérer tous les capitaines disponibles avec le bon type de bateau
        available_captains = Captain.objects.filter(
            is_available=True,
            boat__boat_type=trip_request.boat_type,
            boat__is_available=True,
            boat__capacity__gte=trip_request.passenger_count
        ).select_related('user', 'boat')

        for captain in available_captains:
            if captain.rate_per_hour:
                # Calcul pour mise à disposition (par heure)
                base_price = trip_request.duration_hours * captain.rate_per_hour

                # Créer le devis
                quote_data = {
                    'trip_request': trip_request,
                    'captain': captain,
                    'boat': captain.boat,
                    'base_price': base_price,
                    'distance_km': trip_request.distance_km or 0,
                    'rate_used': captain.rate_per_hour,
                }

                quote = TripQuote.objects.create(**quote_data)
                quotes.append(TripQuoteSerializer(quote).data)

        return quotes


class ShuttleTripRequestView(APIView):
    """Gestion des navettes gratuites"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Créer une demande de navette gratuite"""
        if not hasattr(request.user, 'client'):
            return Response(
                {'error': 'Seuls les clients peuvent créer des demandes de navettes'},
                status=status.HTTP_403_FORBIDDEN
            )

        data = request.data.copy()
        data['client'] = request.user.client.pk

        serializer = ShuttleTripRequestSerializer(data=data)
        if serializer.is_valid():
            trip_request = serializer.save()

            # Calculer la distance vers l'établissement
            trip_request.calculate_distance()

            return Response({
                'trip_request': ShuttleTripRequestSerializer(trip_request).data,
                'message': 'Demande de navette créée. L\'établissement sera notifié.',
                'distance_to_establishment': f"{trip_request.distance_km} km"
            }, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get(self, request):
        """Lister les demandes de navettes (pour les établissements)"""
        if not hasattr(request.user, 'establishment'):
            return Response(
                {'error': 'Seuls les établissements peuvent voir les demandes de navettes'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Récupérer les demandes en attente pour cet établissement
        shuttle_requests = ShuttleTripRequest.objects.filter(
            establishment=request.user.establishment,
            status=TripRequest.Status.PENDING
        ).order_by('-created_at')

        serializer = ShuttleTripRequestSerializer(shuttle_requests, many=True)
        return Response(serializer.data)


class TripRequestDetailView(APIView):
    """Gestion des détails d'une demande de course"""
    permission_classes = [IsAuthenticated]

    def get(self, request, pk):
        """Récupérer les détails d'une demande avec ses devis"""
        trip_request = get_object_or_404(TripRequest, pk=pk)

        # Vérifier les permissions
        if not (hasattr(request.user, 'client') and trip_request.client == request.user.client):
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Sélectionner le bon serializer selon le type
        if trip_request.trip_type == TripRequest.TripType.SIMPLE:
            trip_request = SimpleTripRequest.objects.get(pk=pk)
            serializer = SimpleTripRequestSerializer(trip_request)
        elif trip_request.trip_type == TripRequest.TripType.HOURLY:
            trip_request = HourlyTripRequest.objects.get(pk=pk)
            serializer = HourlyTripRequestSerializer(trip_request)
        elif trip_request.trip_type == TripRequest.TripType.SHUTTLE:
            trip_request = ShuttleTripRequest.objects.get(pk=pk)
            serializer = ShuttleTripRequestSerializer(trip_request)
        else:
            return Response({'error': 'Type de course non reconnu'}, status=status.HTTP_400_BAD_REQUEST)

        # Récupérer les devis associés
        quotes = TripQuote.objects.filter(trip_request=trip_request).order_by('base_price')
        quotes_data = TripQuoteSerializer(quotes, many=True).data

        return Response({
            'trip_request': serializer.data,
            'quotes': quotes_data
        })

    def patch(self, request, pk):
        """Mettre à jour le statut d'une demande"""
        trip_request = get_object_or_404(TripRequest, pk=pk)

        # Vérifier les permissions selon le type d'utilisateur
        new_status = request.data.get('status')

        if hasattr(request.user, 'client') and trip_request.client == request.user.client:
            # Le client peut annuler sa demande
            if new_status == TripRequest.Status.CANCELLED:
                trip_request.status = new_status
                trip_request.save()
                return Response({'message': 'Demande annulée'})

        elif hasattr(request.user, 'establishment'):
            # L'établissement peut accepter/refuser les navettes
            if (trip_request.trip_type == TripRequest.TripType.SHUTTLE and
                hasattr(trip_request, 'shuttletriprequest') and
                trip_request.shuttletriprequest.establishment == request.user.establishment):

                if new_status in [TripRequest.Status.ACCEPTED, TripRequest.Status.REJECTED]:
                    trip_request.status = new_status
                    trip_request.save()
                    return Response({'message': f'Demande {new_status.lower()}'})

        return Response(
            {'error': 'Action non autorisée'},
            status=status.HTTP_403_FORBIDDEN
        )


class TripQuoteAcceptView(APIView):
    """Accepter un devis de course"""
    permission_classes = [IsAuthenticated]

    def post(self, request, quote_id):
        """Accepter un devis et créer la course"""
        quote = get_object_or_404(TripQuote, pk=quote_id)
        trip_request = quote.trip_request

        # Vérifier que c'est le client qui fait la demande
        if not (hasattr(request.user, 'client') and trip_request.client == request.user.client):
            return Response(
                {'error': 'Seul le client peut accepter un devis'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Vérifier que la demande est encore en attente
        if trip_request.status != TripRequest.Status.PENDING:
            return Response(
                {'error': 'Cette demande n\'est plus disponible'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Vérifier que le devis est encore disponible
        if not quote.is_available:
            return Response(
                {'error': 'Ce devis n\'est plus disponible'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Créer la course officielle
        trip_data = {
            'client': trip_request.client,
            'captain': quote.captain,
            'boat': quote.boat,
            'start_location': trip_request.departure_location.get('city_name', ''),
            'end_location': trip_request.arrival_location.get('city_name', ''),
            'passenger_count': trip_request.passenger_count,
            'base_price': quote.base_price,
            'total_price': quote.base_price,
            'status': Trip.Status.ACCEPTED
        }

        # Définir les heures selon le type de course
        if trip_request.trip_type == TripRequest.TripType.SIMPLE:
            simple_request = SimpleTripRequest.objects.get(pk=trip_request.pk)
            if simple_request.scheduled_date and simple_request.scheduled_time:
                # Course programmée
                from datetime import datetime
                scheduled_datetime = datetime.combine(
                    simple_request.scheduled_date,
                    simple_request.scheduled_time
                )
                trip_data['scheduled_start_time'] = timezone.make_aware(scheduled_datetime)
                trip_data['scheduled_end_time'] = trip_data['scheduled_start_time'] + timedelta(hours=2)
            else:
                # Course immédiate
                trip_data['scheduled_start_time'] = timezone.now() + timedelta(minutes=30)
                trip_data['scheduled_end_time'] = trip_data['scheduled_start_time'] + timedelta(hours=2)

        elif trip_request.trip_type == TripRequest.TripType.HOURLY:
            hourly_request = HourlyTripRequest.objects.get(pk=trip_request.pk)
            from datetime import datetime
            start_datetime = datetime.combine(hourly_request.start_date, datetime.min.time())
            trip_data['scheduled_start_time'] = timezone.make_aware(start_datetime)
            trip_data['scheduled_end_time'] = trip_data['scheduled_start_time'] + timedelta(hours=hourly_request.duration_hours)

        # Créer la course
        trip = Trip.objects.create(**trip_data)

        # Marquer la demande comme acceptée
        trip_request.status = TripRequest.Status.ACCEPTED
        trip_request.save()

        # Marquer tous les autres devis comme non disponibles
        TripQuote.objects.filter(trip_request=trip_request).update(is_available=False)

        return Response({
            'message': 'Devis accepté et course créée',
            'trip_id': trip.id,
            'trip': TripSerializer(trip).data
        }, status=status.HTTP_201_CREATED)

    def patch(self, request, pk):
        """Mettre à jour le statut d'une demande"""
        trip_request = get_object_or_404(TripRequest, pk=pk)

        # Vérifier les permissions selon le type d'utilisateur
        new_status = request.data.get('status')

        if hasattr(request.user, 'client') and trip_request.client == request.user.client:
            # Le client peut annuler sa demande
            if new_status == TripRequest.Status.CANCELLED:
                trip_request.status = new_status
                trip_request.save()
                return Response({'message': 'Demande annulée'})

        elif hasattr(request.user, 'establishment'):
            # L'établissement peut accepter/refuser les navettes
            if (trip_request.trip_type == TripRequest.TripType.SHUTTLE and
                hasattr(trip_request, 'shuttletriprequest') and
                trip_request.shuttletriprequest.establishment == request.user.establishment):

                if new_status in [TripRequest.Status.ACCEPTED, TripRequest.Status.REJECTED]:
                    trip_request.status = new_status
                    trip_request.save()
                    return Response({'message': f'Demande {new_status.lower()}'})

        return Response(
            {'error': 'Action non autorisée'},
            status=status.HTTP_403_FORBIDDEN
        )


class TripQuoteAcceptView(APIView):
    """Accepter un devis de course"""
    permission_classes = [IsAuthenticated]

    def post(self, request, quote_id):
        """Accepter un devis et créer la course"""
        quote = get_object_or_404(TripQuote, pk=quote_id)
        trip_request = quote.trip_request

        # Vérifier que c'est le client qui fait la demande
        if not (hasattr(request.user, 'client') and trip_request.client == request.user.client):
            return Response(
                {'error': 'Seul le client peut accepter un devis'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Vérifier que la demande est encore en attente
        if trip_request.status != TripRequest.Status.PENDING:
            return Response(
                {'error': 'Cette demande n\'est plus disponible'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Vérifier que le devis est encore disponible
        if not quote.is_available:
            return Response(
                {'error': 'Ce devis n\'est plus disponible'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Créer la course officielle
        trip_data = {
            'client': trip_request.client,
            'captain': quote.captain,
            'boat': quote.boat,
            'start_location': trip_request.departure_location.get('city_name', ''),
            'end_location': trip_request.arrival_location.get('city_name', ''),
            'passenger_count': trip_request.passenger_count,
            'base_price': quote.base_price,
            'total_price': quote.base_price,
            'status': Trip.Status.ACCEPTED
        }

        # Définir les heures selon le type de course
        if trip_request.trip_type == TripRequest.TripType.SIMPLE:
            simple_request = SimpleTripRequest.objects.get(pk=trip_request.pk)
            if simple_request.scheduled_date and simple_request.scheduled_time:
                # Course programmée
                from datetime import datetime
                scheduled_datetime = datetime.combine(
                    simple_request.scheduled_date,
                    simple_request.scheduled_time
                )
                trip_data['scheduled_start_time'] = timezone.make_aware(scheduled_datetime)
                trip_data['scheduled_end_time'] = trip_data['scheduled_start_time'] + timedelta(hours=2)
            else:
                # Course immédiate
                trip_data['scheduled_start_time'] = timezone.now() + timedelta(minutes=30)
                trip_data['scheduled_end_time'] = trip_data['scheduled_start_time'] + timedelta(hours=2)

        elif trip_request.trip_type == TripRequest.TripType.HOURLY:
            hourly_request = HourlyTripRequest.objects.get(pk=trip_request.pk)
            from datetime import datetime
            start_datetime = datetime.combine(hourly_request.start_date, datetime.min.time())
            trip_data['scheduled_start_time'] = timezone.make_aware(start_datetime)
            trip_data['scheduled_end_time'] = trip_data['scheduled_start_time'] + timedelta(hours=hourly_request.duration_hours)

        # Créer la course
        trip = Trip.objects.create(**trip_data)

        # Marquer la demande comme acceptée
        trip_request.status = TripRequest.Status.ACCEPTED
        trip_request.save()

        # Marquer tous les autres devis comme non disponibles
        TripQuote.objects.filter(trip_request=trip_request).update(is_available=False)

        return Response({
            'message': 'Devis accepté et course créée',
            'trip_id': trip.id,
            'trip': TripSerializer(trip).data
        }, status=status.HTTP_201_CREATED)


class ExpiredTripRequestCleanupView(APIView):
    """Vue pour nettoyer les demandes expirées (à appeler périodiquement)"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Marquer les demandes expirées comme EXPIRED"""
        if not request.user.is_staff:
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Trouver toutes les demandes expirées
        expired_requests = TripRequest.objects.filter(
            status=TripRequest.Status.PENDING,
            expires_at__lt=timezone.now()
        )

        count = expired_requests.count()
        expired_requests.update(status=TripRequest.Status.EXPIRED)

        # Marquer les devis associés comme non disponibles
        TripQuote.objects.filter(
            trip_request__in=expired_requests,
            is_available=True
        ).update(is_available=False)

        return Response({
            'message': f'{count} demandes expirées nettoyées',
            'expired_count': count
        })
