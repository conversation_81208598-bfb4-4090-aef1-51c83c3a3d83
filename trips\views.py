from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from .models import Trip, Shuttle
from .serializers import (
    TripSerializer, TripListSerializer,
    ShuttleSerializer, ShuttleListSerializer
)
from accounts.models import <PERSON><PERSON>, Captain, Establishment
from boats.models import Boat
from django.db.models import Q
from datetime import datetime, timedelta
from django.utils import timezone

class TripListCreateView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        # Filtrer selon le type d'utilisateur
        if hasattr(request.user, 'client'):
            trips = Trip.objects.filter(client=request.user.client)
        elif hasattr(request.user, 'captain'):
            trips = Trip.objects.filter(captain=request.user.captain)
        elif hasattr(request.user, 'establishment'):
            trips = Trip.objects.filter(establishment=request.user.establishment)
        else:
            return Response(
                {'error': 'Type d\'utilisateur non autorisé'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Appliquer les filtres
        status_filter = request.query_params.get('status')
        date_from = request.query_params.get('date_from')
        date_to = request.query_params.get('date_to')

        if status_filter:
            trips = trips.filter(status=status_filter)
        if date_from:
            trips = trips.filter(scheduled_start_time__date__gte=date_from)
        if date_to:
            trips = trips.filter(scheduled_start_time__date__lte=date_to)

        serializer = TripListSerializer(trips, many=True)
        return Response(serializer.data)

    def post(self, request):
        if not hasattr(request.user, 'client'):
            return Response(
                {'error': 'Seuls les clients peuvent créer des courses'},
                status=status.HTTP_403_FORBIDDEN
            )

        data = request.data.copy()
        data['client'] = request.user.client.pk
        data['status'] = 'PENDING'

        # Vérifier la disponibilité du bateau
        boat = get_object_or_404(Boat, pk=data.get('boat'))
        if boat.status != 'AVAILABLE':
            return Response(
                {'error': 'Ce bateau n\'est pas disponible'},
                status=status.HTTP_400_BAD_REQUEST
            )

        serializer = TripSerializer(data=data)
        if serializer.is_valid():
            trip = serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class TripDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, pk):
        trip = get_object_or_404(Trip, pk=pk)
        
        # Vérifier les permissions
        if not self._has_permission(request.user, trip):
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = TripSerializer(trip)
        return Response(serializer.data)

    def patch(self, request, pk):
        trip = get_object_or_404(Trip, pk=pk)

        if not self._has_permission(request.user, trip):
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Vérifier les transitions de statut autorisées
        new_status = request.data.get('status')
        if new_status and not self._is_valid_status_transition(trip, new_status, request.user):
            return Response(
                {'error': 'Transition de statut non autorisée'},
                status=status.HTTP_400_BAD_REQUEST
            )

        serializer = TripSerializer(trip, data=request.data, partial=True)
        if serializer.is_valid():
            trip = serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        trip = get_object_or_404(Trip, pk=pk)

        if not self._has_permission(request.user, trip):
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )

        if trip.status not in ['PENDING', 'SCHEDULED']:
            return Response(
                {'error': 'Impossible d\'annuler une course en cours ou terminée'},
                status=status.HTTP_400_BAD_REQUEST
            )

        trip.status = 'CANCELLED'
        trip.save()
        return Response(status=status.HTTP_204_NO_CONTENT)

    def _has_permission(self, user, trip):
        return (
            hasattr(user, 'client') and trip.client == user.client or
            hasattr(user, 'captain') and trip.captain == user.captain or
            hasattr(user, 'establishment') and trip.establishment == user.establishment
        )

    def _is_valid_status_transition(self, trip, new_status, user):
        valid_transitions = {
            'PENDING': {
                'client': ['CANCELLED'],
                'captain': ['ACCEPTED', 'REJECTED'],
                'establishment': ['ACCEPTED', 'REJECTED']
            },
            'ACCEPTED': {
                'captain': ['IN_PROGRESS', 'CANCELLED'],
                'establishment': ['IN_PROGRESS', 'CANCELLED']
            },
            'IN_PROGRESS': {
                'captain': ['COMPLETED'],
                'establishment': ['COMPLETED']
            }
        }

        user_type = 'client' if hasattr(user, 'client') else \
                   'captain' if hasattr(user, 'captain') else \
                   'establishment' if hasattr(user, 'establishment') else None

        return (
            trip.status in valid_transitions and
            user_type in valid_transitions[trip.status] and
            new_status in valid_transitions[trip.status][user_type]
        )

class TripTrackingView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, pk):
        trip = get_object_or_404(Trip, pk=pk)

        if not (
            hasattr(request.user, 'client') and trip.client == request.user.client or
            hasattr(request.user, 'captain') and trip.captain == request.user.captain or
            hasattr(request.user, 'establishment') and trip.establishment == request.user.establishment
        ):
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )

        return Response({
            'trip_id': trip.pk,
            'status': trip.status,
            'current_location': trip.current_location,
            'tracking_data': trip.tracking_data,
            'estimated_arrival_time': trip.estimated_arrival_time
        })

    def post(self, request, pk):
        trip = get_object_or_404(Trip, pk=pk)

        if not (hasattr(request.user, 'captain') and trip.captain == request.user.captain):
            return Response(
                {'error': 'Seul le capitaine peut mettre à jour la position'},
                status=status.HTTP_403_FORBIDDEN
            )

        if trip.status != 'IN_PROGRESS':
            return Response(
                {'error': 'La course doit être en cours pour mettre à jour la position'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Mettre à jour la position et les données de suivi
        trip.current_location = request.data.get('current_location')
        trip.tracking_data = request.data.get('tracking_data')
        trip.estimated_arrival_time = request.data.get('estimated_arrival_time')
        trip.save()

        return Response({
            'message': 'Position mise à jour avec succès',
            'current_location': trip.current_location,
            'estimated_arrival_time': trip.estimated_arrival_time
        })

class ShuttleListCreateView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        if hasattr(request.user, 'establishment'):
            shuttles = Shuttle.objects.filter(establishment=request.user.establishment)
        elif hasattr(request.user, 'captain'):
            shuttles = Shuttle.objects.filter(captain=request.user.captain)
        else:
            shuttles = Shuttle.objects.filter(status='SCHEDULED')

        # Appliquer les filtres
        date = request.query_params.get('date')
        route = request.query_params.get('route')
        available_seats = request.query_params.get('available_seats')

        if date:
            shuttles = shuttles.filter(departure_time__date=date)
        if route:
            shuttles = shuttles.filter(
                Q(departure_location__icontains=route) |
                Q(arrival_location__icontains=route)
            )
        if available_seats:
            shuttles = shuttles.filter(
                max_capacity__gte=models.F('current_bookings') + int(available_seats)
            )

        serializer = ShuttleListSerializer(shuttles, many=True)
        return Response(serializer.data)

    def post(self, request):
        if not hasattr(request.user, 'establishment'):
            return Response(
                {'error': 'Seuls les établissements peuvent créer des navettes'},
                status=status.HTTP_403_FORBIDDEN
            )

        data = request.data.copy()
        data['establishment'] = request.user.establishment.pk
        data['status'] = 'SCHEDULED'

        serializer = ShuttleSerializer(data=data)
        if serializer.is_valid():
            shuttle = serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class ShuttleDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, pk):
        shuttle = get_object_or_404(Shuttle, pk=pk)
        serializer = ShuttleSerializer(shuttle)
        return Response(serializer.data)

    def patch(self, request, pk):
        shuttle = get_object_or_404(Shuttle, pk=pk)

        if not (
            hasattr(request.user, 'establishment') and 
            shuttle.establishment == request.user.establishment
        ):
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = ShuttleSerializer(shuttle, data=request.data, partial=True)
        if serializer.is_valid():
            shuttle = serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        shuttle = get_object_or_404(Shuttle, pk=pk)

        if not (
            hasattr(request.user, 'establishment') and 
            shuttle.establishment == request.user.establishment
        ):
            return Response(
                {'error': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )

        if shuttle.status not in ['SCHEDULED']:
            return Response(
                {'error': 'Impossible d\'annuler une navette en cours ou terminée'},
                status=status.HTTP_400_BAD_REQUEST
            )

        shuttle.status = 'CANCELLED'
        shuttle.save()
        return Response(status=status.HTTP_204_NO_CONTENT)
