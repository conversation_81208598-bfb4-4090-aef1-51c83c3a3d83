from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import logging
import stripe
from django.conf import settings
from .models import Payment
from .stripe_utils import handle_webhook_event
# Utiliser directement le modèle Trip de l'application trips
from trips.models import Trip
from accounts.models import Captain, Client, Establishment, User

logger = logging.getLogger(__name__)

@method_decorator(csrf_exempt, name='dispatch')
class StripeWebhookView(APIView):
    """Vue pour gérer les webhooks Stripe sans authentification"""
    permission_classes = []  # Pas d'authentification requise

    def post(self, request, *args, **kwargs):
        """Gère les événements webhook de Stripe"""
        logger.info("Webhook Stripe reçu")

        payload = request.body
        sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')

        # Journaliser les en-têtes pour le débogage
        logger.debug(f"En-têtes de la requête: {request.META}")

        if not sig_header:
            logger.error("Pas d'en-tête de signature Stripe")
            return Response({'error': 'Pas d\'en-tête de signature Stripe'}, status=status.HTTP_400_BAD_REQUEST)

        # Journaliser la signature pour le débogage
        logger.debug(f"Signature Stripe: {sig_header}")
        logger.debug(f"Clé secrète de webhook: {settings.STRIPE_WEBHOOK_SECRET[:5]}...")

        try:
            event = handle_webhook_event(payload, sig_header)

            if isinstance(event, dict) and 'error' in event:
                logger.error(f"Erreur de webhook: {event['error']}")
                return Response({'error': event['error']}, status=status.HTTP_400_BAD_REQUEST)

            logger.info(f"Événement webhook reçu: {event.type}")
            logger.debug(f"Données de l'événement: {event.data.object}")
        except Exception as e:
            logger.error(f"Erreur lors du traitement du webhook: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

        # Gérer l'événement
        try:
            if event.type == 'payment_intent.succeeded':
                self._handle_payment_intent_succeeded(event.data.object)

            elif event.type == 'payment_intent.payment_failed':
                self._handle_payment_intent_failed(event.data.object)

            elif event.type == 'checkout.session.completed':
                self._handle_checkout_session_completed(event.data.object)

            elif event.type == 'checkout.session.expired':
                self._handle_checkout_session_expired(event.data.object)

            elif event.type == 'charge.refunded':
                self._handle_charge_refunded(event.data.object)

            elif event.type == 'charge.dispute.created':
                self._handle_dispute_created(event.data.object)

            elif event.type == 'account.updated':
                self._handle_account_updated(event.data.object)

            elif event.type == 'transfer.created':
                self._handle_transfer_created(event.data.object)

            elif event.type == 'transfer.failed':
                self._handle_transfer_failed(event.data.object)

            # Vous pouvez ajouter d'autres types d'événements selon vos besoins

        except Exception as e:
            logger.error(f"Erreur lors du traitement de l'événement {event.type}: {str(e)}")
            # Nous retournons quand même un 200 pour que Stripe ne réessaie pas

        # Retourner une réponse pour accuser réception de l'événement
        return HttpResponse(status=200)

    def _handle_payment_intent_succeeded(self, payment_intent):
        """Gère l'événement payment_intent.succeeded"""
        # Récupérer le paiement à partir des métadonnées
        payment_id = payment_intent.metadata.get('payment_id')

        if not payment_id:
            logger.warning(f"Aucun payment_id trouvé dans les métadonnées du payment_intent {payment_intent.id}")
            return

        try:
            payment = Payment.objects.get(id=payment_id)

            # Mettre à jour le statut du paiement
            payment.status = 'COMPLETED'

            # Stocker l'URL du reçu si disponible
            if hasattr(payment_intent, 'charges') and payment_intent.charges.data:
                charge = payment_intent.charges.data[0]
                if hasattr(charge, 'receipt_url'):
                    payment.receipt_url = charge.receipt_url

            # Stocker le type de méthode de paiement
            if hasattr(payment_intent, 'payment_method_types') and payment_intent.payment_method_types:
                payment.stripe_payment_method_type = payment_intent.payment_method_types[0]

            payment.save()

            logger.info(f"Paiement {payment_id} marqué comme COMPLETED")

            # Envoyer une notification de paiement réussi
            try:
                from notifications.services import create_payment_success_notification
                create_payment_success_notification(payment)
                logger.info(f"Notification de paiement réussi envoyée pour le paiement {payment_id}")
            except Exception as e:
                logger.error(f"Erreur lors de l'envoi de la notification de paiement réussi: {str(e)}")

            # Si c'est un paiement pour une réservation, mettre à jour le statut de la réservation
            if payment.booking and payment.booking.status == 'PENDING':
                payment.booking.status = 'ACCEPTED'
                payment.booking.save()
                logger.info(f"Réservation {payment.booking.id} mise à jour vers ACCEPTED")

            # Si c'est un paiement à un capitaine, créer un transfert
            if payment.booking and payment.booking.boat and payment.booking.boat.captain:
                self._create_captain_transfer(payment, payment_intent)

        except Payment.DoesNotExist:
            logger.error(f"Paiement {payment_id} non trouvé")

    def _create_captain_transfer(self, payment, payment_intent):
        """Crée un transfert vers le compte du capitaine"""
        captain = payment.booking.boat.captain

        if not captain.stripe_connect_id:
            logger.warning(f"Le capitaine {captain.id} n'a pas d'ID Stripe Connect")
            return

        # Calculer la part du capitaine (par exemple, 80% du paiement)
        captain_amount = int(payment_intent.amount * 0.8)

        # Créer un transfert vers le capitaine
        from .stripe_utils import create_transfer
        transfer = create_transfer(
            amount=captain_amount,
            destination=captain.stripe_connect_id,
            source_transaction=payment_intent.id,
            description=f"Paiement pour la réservation #{payment.booking.id}",
            metadata={
                'payment_id': str(payment.id),
                'booking_id': str(payment.booking.id),
                'captain_id': str(captain.id)
            }
        )

        if 'error' not in transfer:
            # Mettre à jour les gains du capitaine
            captain.earnings += captain_amount / 100  # Convertir les centimes en euros
            captain.save()
            logger.info(f"Transfert de {captain_amount/100}€ créé pour le capitaine {captain.id}")
        else:
            logger.error(f"Erreur lors du transfert au capitaine {captain.id}: {transfer['error']}")

    def _handle_payment_intent_failed(self, payment_intent):
        """Gère l'événement payment_intent.payment_failed"""
        payment_id = payment_intent.metadata.get('payment_id')

        if not payment_id:
            logger.warning(f"Aucun payment_id trouvé dans les métadonnées du payment_intent {payment_intent.id}")
            return

        try:
            payment = Payment.objects.get(id=payment_id)
            payment.status = 'FAILED'

            # Stocker les détails de l'échec
            error_message = None
            if hasattr(payment_intent, 'last_payment_error') and payment_intent.last_payment_error:
                error_message = payment_intent.last_payment_error.message
                payment.metadata = {
                    **payment.metadata,
                    'failure_message': error_message,
                    'failure_code': payment_intent.last_payment_error.code
                }

            payment.save()
            logger.info(f"Paiement {payment_id} marqué comme FAILED")

            # Envoyer une notification de paiement échoué
            try:
                from notifications.services import create_payment_failed_notification
                create_payment_failed_notification(payment, error_message)
                logger.info(f"Notification de paiement échoué envoyée pour le paiement {payment_id}")
            except Exception as e:
                logger.error(f"Erreur lors de l'envoi de la notification de paiement échoué: {str(e)}")

        except Payment.DoesNotExist:
            logger.error(f"Paiement {payment_id} non trouvé")

    def _handle_checkout_session_completed(self, session):
        """Gère l'événement checkout.session.completed"""
        # Vérifier si un paiement existe déjà avec cet ID de session
        payment_id = session.metadata.get('payment_id')
        if payment_id:
            try:
                payment = Payment.objects.get(id=payment_id)
                payment.status = 'COMPLETED'
                payment.stripe_payment_id = session.payment_intent

                # Mettre à jour les informations du client
                if hasattr(session, 'customer'):
                    payment.stripe_customer_id = session.customer

                payment.save()
                logger.info(f"Paiement existant {payment_id} mis à jour")

                # Mettre à jour la réservation si nécessaire
                if payment.booking and payment.booking.status == 'PENDING':
                    payment.booking.status = 'ACCEPTED'
                    payment.booking.save()
                    logger.info(f"Réservation {payment.booking.id} mise à jour vers ACCEPTED")

                return
            except Payment.DoesNotExist:
                pass

        # Récupérer le type de paiement et l'ID de réservation à partir des métadonnées
        payment_type = session.metadata.get('payment_type')
        booking_id = session.metadata.get('booking_id')
        wallet_id = session.metadata.get('wallet_id')
        transaction_type = session.metadata.get('transaction_type')

        # Créer un enregistrement de paiement
        payment_data = {
            'amount': session.amount_total / 100,  # Convertir les centimes en euros
            'type': payment_type or 'TRIP',
            'status': 'COMPLETED',
            'stripe_payment_id': session.payment_intent,
            'metadata': {
                'checkout_session_id': session.id,
                **session.metadata
            }
        }

        if hasattr(session, 'customer'):
            payment_data['stripe_customer_id'] = session.customer

        if booking_id:
            try:
                booking = Trip.objects.get(id=booking_id)
                payment_data['booking'] = booking

                # Mettre à jour le statut de la réservation
                if booking.status == 'PENDING':
                    booking.status = 'ACCEPTED'
                    booking.save()
                    logger.info(f"Réservation {booking_id} mise à jour vers ACCEPTED")
            except Trip.DoesNotExist:
                logger.error(f"Réservation {booking_id} non trouvée")

        # Créer le paiement
        payment = Payment.objects.create(**payment_data)
        logger.info(f"Nouveau paiement {payment.id} créé pour la session {session.id}")

        # Si c'est un achat de crédits, mettre à jour le portefeuille
        if wallet_id and transaction_type == 'PURCHASE':
            try:
                # Utiliser la classe CreditWallet définie dans views.py
                from .views import CreditWallet
                wallet = CreditWallet.objects.get(id=wallet_id)

                # Créer une transaction
                from .models import Transaction
                transaction = Transaction.objects.create(
                    wallet=wallet,
                    amount=payment_data['amount'],
                    type='PURCHASE',
                    status='COMPLETED',
                    metadata={
                        'checkout_session_id': session.id,
                        'payment_id': str(payment.id)
                    }
                )

                # Mettre à jour le solde du portefeuille
                wallet.purchase_credits(payment_data['amount'])
                logger.info(f"Crédits achetés pour le portefeuille {wallet_id}: {payment_data['amount']}")
            except Exception as e:
                logger.error(f"Erreur lors de l'achat de crédits: {str(e)}")

    def _handle_checkout_session_expired(self, session):
        """Gère l'événement checkout.session.expired"""
        payment_id = session.metadata.get('payment_id')
        if payment_id:
            try:
                payment = Payment.objects.get(id=payment_id)
                payment.status = 'FAILED'
                payment.metadata = {
                    **payment.metadata,
                    'failure_reason': 'Session expirée'
                }
                payment.save()
                logger.info(f"Paiement {payment_id} marqué comme FAILED (session expirée)")
            except Payment.DoesNotExist:
                logger.error(f"Paiement {payment_id} non trouvé")

    def _handle_charge_refunded(self, charge):
        """Gère l'événement charge.refunded"""
        # Trouver le paiement associé à cette charge
        payment_intent_id = charge.payment_intent
        if not payment_intent_id:
            logger.warning(f"Aucun payment_intent trouvé pour la charge {charge.id}")
            return

        try:
            payment = Payment.objects.filter(stripe_payment_id=payment_intent_id).first()
            if not payment:
                logger.error(f"Aucun paiement trouvé pour le payment_intent {payment_intent_id}")
                return

            # Vérifier si c'est un remboursement partiel ou complet
            is_full_refund = charge.amount_refunded == charge.amount

            if is_full_refund:
                payment.status = 'REFUNDED'
                payment.refund_amount = payment.amount
                logger.info(f"Paiement {payment.id} remboursé intégralement")
            else:
                payment.status = 'PARTIALLY_REFUNDED'
                payment.refund_amount = charge.amount_refunded / 100  # Convertir les centimes en euros
                logger.info(f"Paiement {payment.id} remboursé partiellement: {payment.refund_amount}€")

            # Stocker l'ID du remboursement
            if hasattr(charge, 'refunds') and charge.refunds.data:
                payment.refund_id = charge.refunds.data[0].id

            payment.save()

            # Envoyer une notification de remboursement
            try:
                from notifications.services import create_refund_completed_notification
                create_refund_completed_notification(payment)
                logger.info(f"Notification de remboursement envoyée pour le paiement {payment.id}")
            except Exception as e:
                logger.error(f"Erreur lors de l'envoi de la notification de remboursement: {str(e)}")

            # Si c'est un paiement pour une réservation, mettre à jour le statut de la réservation
            if payment.booking and payment.booking.status == 'ACCEPTED':
                payment.booking.status = 'CANCELED'
                payment.booking.save()
                logger.info(f"Réservation {payment.booking.id} annulée suite au remboursement")

        except Exception as e:
            logger.error(f"Erreur lors du traitement du remboursement: {str(e)}")

    def _handle_dispute_created(self, dispute):
        """Gère l'événement charge.dispute.created"""
        # Trouver le paiement associé à cette contestation
        payment_intent_id = dispute.payment_intent
        if not payment_intent_id:
            logger.warning(f"Aucun payment_intent trouvé pour la contestation {dispute.id}")
            return

        try:
            payment = Payment.objects.filter(stripe_payment_id=payment_intent_id).first()
            if not payment:
                logger.error(f"Aucun paiement trouvé pour le payment_intent {payment_intent_id}")
                return

            # Mettre à jour le paiement avec les informations de la contestation
            payment.metadata = {
                **payment.metadata,
                'dispute': {
                    'id': dispute.id,
                    'reason': dispute.reason,
                    'status': dispute.status,
                    'amount': dispute.amount / 100,  # Convertir les centimes en euros
                    'created': dispute.created
                }
            }
            payment.save()
            logger.info(f"Contestation {dispute.id} enregistrée pour le paiement {payment.id}")

            # Notifier l'administrateur (vous pourriez implémenter cela plus tard)

        except Exception as e:
            logger.error(f"Erreur lors du traitement de la contestation: {str(e)}")

    def _handle_account_updated(self, account):
        """Gère l'événement account.updated"""
        # Trouver le capitaine associé à ce compte
        try:
            captain = Captain.objects.filter(stripe_connect_id=account.id).first()
            if not captain:
                logger.warning(f"Aucun capitaine trouvé pour le compte Stripe {account.id}")
                return

            # Mettre à jour le statut du compte
            metadata = {}
            if hasattr(captain, 'metadata') and captain.metadata:
                metadata = captain.metadata

            metadata['stripe_account_status'] = {
                'charges_enabled': account.charges_enabled,
                'payouts_enabled': account.payouts_enabled,
                'requirements': account.requirements,
                'updated': account.created
            }
            captain.metadata = metadata
            captain.save()
            logger.info(f"Statut du compte Stripe mis à jour pour le capitaine {captain.id}")

        except Exception as e:
            logger.error(f"Erreur lors du traitement de la mise à jour du compte: {str(e)}")

    def _handle_transfer_created(self, transfer):
        """Gère l'événement transfer.created"""
        # Vérifier si c'est un transfert vers un capitaine
        captain_id = transfer.metadata.get('captain_id')
        if not captain_id:
            return

        try:
            captain = Captain.objects.get(id=captain_id)

            # Enregistrer le transfert
            from .models import Transaction
            Transaction.objects.create(
                amount=transfer.amount / 100,  # Convertir les centimes en euros
                type='TRANSFER',
                status='COMPLETED',
                metadata={
                    'transfer_id': transfer.id,
                    'captain_id': captain_id,
                    'payment_id': transfer.metadata.get('payment_id'),
                    'booking_id': transfer.metadata.get('booking_id')
                }
            )
            logger.info(f"Transfert {transfer.id} enregistré pour le capitaine {captain_id}")

        except Exception as e:
            logger.error(f"Erreur lors de l'enregistrement du transfert: {str(e)}")

    def _handle_transfer_failed(self, transfer):
        """Gère l'événement transfer.failed"""
        # Vérifier si c'est un transfert vers un capitaine
        captain_id = transfer.metadata.get('captain_id')
        if not captain_id:
            return

        try:
            captain = Captain.objects.get(id=captain_id)

            # Enregistrer l'échec du transfert
            from .models import Transaction
            Transaction.objects.create(
                amount=transfer.amount / 100,  # Convertir les centimes en euros
                type='TRANSFER',
                status='FAILED',
                metadata={
                    'transfer_id': transfer.id,
                    'captain_id': captain_id,
                    'payment_id': transfer.metadata.get('payment_id'),
                    'booking_id': transfer.metadata.get('booking_id'),
                    'failure_code': transfer.failure_code,
                    'failure_message': transfer.failure_message
                }
            )
            logger.info(f"Échec du transfert {transfer.id} enregistré pour le capitaine {captain_id}")

            # Notifier l'administrateur (vous pourriez implémenter cela plus tard)

        except Exception as e:
            logger.error(f"Erreur lors de l'enregistrement de l'échec du transfert: {str(e)}")
