# Setup du Projet Commodore

Ce document explique comment configurer le projet Commodore pour le développement.

## 🚀 Setup Rapide pour Nouveaux Développeurs

### Prérequis
- Python 3.8+
- PostgreSQL 12+
- Git

### Installation Automatique

1. **<PERSON><PERSON>r le projet** :
```bash
git clone <url-du-repo>
cd commodore
```

2. **Exécuter le script de setup** :
```powershell
.\scripts\setup_project.ps1
```

3. **C<PERSON>er un superuser** :
```bash
python manage.py createsuperuser
```

4. **Lancer le serveur** :
```bash
python manage.py runserver
```

## 🔧 Résolution des Problèmes de Migration

### Si vous rencontrez des erreurs de migration

Le projet inclut un script de reset complet qui résout tous les problèmes de migrations :

```powershell
.\scripts\reset_django_project.ps1
```

**Ce script va :**
- Supprimer et recréer la base de données
- Supprimer tous les fichiers de migration (sauf `__init__.py`)
- Nettoyer les caches Python
- Recréer les migrations depuis zéro
- Appliquer toutes les migrations

### Pourquoi ce script ?

Ce script garantit que :
1. **Tous les développeurs ont le même état de base de données**
2. **Pas de conflits de migrations entre développeurs**
3. **Setup propre pour nouveaux développeurs**
4. **Résolution automatique des problèmes de dépendances**

## 📁 Structure des Scripts

- `scripts/setup_project.ps1` : Setup initial pour nouveaux développeurs
- `scripts/reset_django_project.ps1` : Reset complet en cas de problème
- `scripts/reset_django_auth.py` : Reset spécifique pour l'authentification

## 🔒 Configuration de la Base de Données

Le projet utilise PostgreSQL avec ces paramètres par défaut :
- **Base de données** : `commodore`
- **Utilisateur** : `postgres`
- **Mot de passe** : `admin`
- **Host** : `localhost`
- **Port** : `5432`

Modifiez le fichier `.env` si vos paramètres sont différents.

## 🆘 En Cas de Problème

1. **Erreur de migration** → Utilisez `.\scripts\reset_django_project.ps1`
2. **Problème d'authentification** → Utilisez `.\scripts\reset_django_auth.py`
3. **Base de données corrompue** → Reset complet avec le script PowerShell

## 📝 Notes pour les Développeurs

- **Ne jamais modifier manuellement les fichiers de migration**
- **Utiliser les scripts fournis pour résoudre les problèmes**
- **Toujours tester les migrations sur une base propre**
- **Documenter les changements de modèles dans les PR**

## 🔄 Workflow de Développement

1. Faire vos modifications de modèles
2. Créer les migrations : `python manage.py makemigrations`
3. Tester sur base propre : `.\scripts\reset_django_project.ps1`
4. Vérifier que tout fonctionne
5. Commit et push

Cette approche garantit que tous les développeurs peuvent travailler sans problème de migration.
