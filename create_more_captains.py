#!/usr/bin/env python
"""
Script pour créer plusieurs capitaines avec des tarifs différents
pour tester le système de devis multiples
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'commodore.settings')
django.setup()

from django.contrib.auth import get_user_model
from accounts.models import Captain
from boats.models import Boat
from decimal import Decimal

User = get_user_model()

def create_captain_with_boat(email, first_name, last_name, rate_per_hour, rate_per_km, boat_name, boat_type, capacity, color):
    """Créer un capitaine avec son bateau"""
    
    # Créer l'utilisateur
    user, created = User.objects.get_or_create(
        email=email,
        defaults={
            'first_name': first_name,
            'last_name': last_name,
            'type': 'CAPTAIN',
            'is_active': True
        }
    )
    
    if created:
        user.set_password('password123')
        user.save()
        print(f"✅ Utilisateur créé: {email}")
    else:
        print(f"ℹ️ Utilisateur existe déjà: {email}")
    
    # <PERSON><PERSON><PERSON> le profil capitaine
    captain, created = Captain.objects.get_or_create(
        user=user,
        defaults={
            'experience': f'{rate_per_hour//10} ans d\'expérience maritime',
            'license_number': f'LIC{user.id:03d}',
            'rate_per_hour': Decimal(str(rate_per_hour)),
            'rate_per_km': Decimal(str(rate_per_km)),
            'is_available': True,
            'availability_status': 'AVAILABLE'
        }
    )
    
    if created:
        print(f"✅ Capitaine créé: {first_name} {last_name}")
    else:
        print(f"ℹ️ Capitaine existe déjà: {first_name} {last_name}")
        # Mettre à jour les tarifs
        captain.rate_per_hour = Decimal(str(rate_per_hour))
        captain.rate_per_km = Decimal(str(rate_per_km))
        captain.save()
    
    # Créer le bateau
    boat, created = Boat.objects.get_or_create(
        captain=captain,
        defaults={
            'name': boat_name,
            'registration_number': f'BN{user.id:04d}',
            'boat_type': boat_type,
            'capacity': capacity,
            'color': color,
            'fuel_type': 'GASOLINE',
            'fuel_consumption': Decimal('10.5'),
            'zone_served': 'Cotonou, Bénin',
            'radius': 50,
            'is_available': True
        }
    )
    
    if created:
        print(f"✅ Bateau créé: {boat_name}")
    else:
        print(f"ℹ️ Bateau existe déjà: {boat_name}")
        # Mettre à jour les infos du bateau
        boat.name = boat_name
        boat.boat_type = boat_type
        boat.capacity = capacity
        boat.color = color
        boat.save()
    
    return captain, boat

def main():
    print("🚢 Création de capitaines avec des tarifs différents...\n")
    
    # Liste des capitaines à créer
    captains_data = [
        {
            'email': '<EMAIL>',
            'first_name': 'Beta',
            'last_name': 'Capitaine',
            'rate_per_hour': 35.00,  # Moins cher
            'rate_per_km': 1.80,
            'boat_name': 'Sea Ray SLX 400',
            'boat_type': 'LUXURY',
            'capacity': 10,
            'color': 'Blanc'
        },
        {
            'email': '<EMAIL>',
            'first_name': 'Gamma',
            'last_name': 'Capitaine',
            'rate_per_hour': 28.00,  # Le moins cher
            'rate_per_km': 1.50,
            'boat_name': 'Beneteau Flyer 7.7 SUNdeck',
            'boat_type': 'TOURISM',
            'capacity': 12,
            'color': 'Bleu'
        },
        {
            'email': '<EMAIL>',
            'first_name': 'Delta',
            'last_name': 'Capitaine',
            'rate_per_hour': 50.00,  # Le plus cher
            'rate_per_km': 3.00,
            'boat_name': 'Yamaha 242X E-Series',
            'boat_type': 'SPEED',
            'capacity': 8,
            'color': 'Rouge'
        },
        {
            'email': '<EMAIL>',
            'first_name': 'Epsilon',
            'last_name': 'Capitaine',
            'rate_per_hour': 40.00,  # Prix moyen
            'rate_per_km': 2.20,
            'boat_name': 'Chaparral 21 H2O',
            'boat_type': 'CLASSIC',
            'capacity': 9,
            'color': 'Vert'
        }
    ]
    
    created_captains = []
    
    for data in captains_data:
        print(f"📋 Création de {data['first_name']} {data['last_name']}...")
        captain, boat = create_captain_with_boat(**data)
        created_captains.append((captain, boat))
        print(f"   💰 Tarif horaire: {data['rate_per_hour']}€/h")
        print(f"   🚤 Bateau: {data['boat_name']} ({data['capacity']} places)")
        print()
    
    print("🎉 Tous les capitaines ont été créés !")
    print("\n📊 Résumé des tarifs horaires:")
    for captain, boat in created_captains:
        print(f"   • {captain.user.first_name} {captain.user.last_name}: {captain.rate_per_hour}€/h - {boat.name}")
    
    print(f"\n✅ {len(created_captains)} capitaines disponibles pour les tests")

if __name__ == '__main__':
    main()
