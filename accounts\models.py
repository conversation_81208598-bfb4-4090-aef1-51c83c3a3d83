from django.contrib.auth.models import AbstractUser, BaseUserManager
from django.db import models
from django.utils.translation import gettext_lazy as _

class UserManager(BaseUserManager):
    def create_user(self, email, password=None, **extra_fields):
        if not email:
            raise ValueError('L\'email est obligatoire')
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        return self.create_user(email, password, **extra_fields)

class User(AbstractUser):
    class Types(models.TextChoices):
        CLIENT = 'CLIENT', _('Client')
        CAPTAIN = 'CAPTAIN', _('Capitaine')
        ESTABLISHMENT = 'ESTABLISHMENT', _('Établissement')

    # Champs de base
    username = None
    email = models.EmailField(_('adresse email'), unique=True)
    phone_number = models.CharField(_('numéro de téléphone'), max_length=15, blank=True)
    type = models.CharField(_('type d\'utilisateur'), max_length=20, choices=Types.choices)
    profile_picture = models.URLField(_('photo de profil'), max_length=500, null=True, blank=True)
    email_verified = models.BooleanField(_('email vérifié'), default=False)
    phone_verified = models.BooleanField(_('téléphone vérifié'), default=False)

    # Authentification sociale
    facebook_id = models.CharField(max_length=50, blank=True, null=True)
    google_id = models.CharField(max_length=50, blank=True, null=True)
    apple_id = models.CharField(max_length=50, blank=True, null=True)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []

    objects = UserManager()

    def __str__(self):
        return self.email

class Client(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, primary_key=True)
    wallet_balance = models.DecimalField(_('solde du portefeuille'), max_digits=10, decimal_places=2, default=0)
    date_of_birth = models.DateField(_('date de naissance'), null=True, blank=True)
    nationality = models.CharField(_('nationalité'), max_length=50, blank=True)
    preferred_language = models.CharField(_('langue préférée'), max_length=10, default='fr')
    emergency_contact_name = models.CharField(_('nom du contact d\'urgence'), max_length=100, blank=True)
    emergency_contact_phone = models.CharField(_('téléphone du contact d\'urgence'), max_length=15, blank=True)

    def __str__(self):
        return f'Client: {self.user.email}'

class Captain(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, primary_key=True)
    experience = models.TextField(_('expérience'))
    average_rating = models.DecimalField(_('note moyenne'), max_digits=3, decimal_places=2, default=0)
    total_trips = models.IntegerField(_('nombre total de courses'), default=0)
    wallet_balance = models.DecimalField(_('solde du portefeuille'), max_digits=10, decimal_places=2, default=0)
    is_available = models.BooleanField(_('disponible'), default=True)
    current_location = models.CharField(_('position actuelle'), max_length=255, blank=True)
    license_number = models.CharField(_('numéro de licence'), max_length=50, blank=True)
    license_expiry_date = models.DateField(_('date d\'expiration de la licence'), null=True, blank=True)
    years_of_experience = models.IntegerField(_('années d\'expérience'), default=0)
    certifications = models.JSONField(_('certifications'), default=list)
    specializations = models.JSONField(_('spécialisations'), default=list)
    availability_status = models.CharField(_('statut de disponibilité'), max_length=20, default='AVAILABLE')
    boat_photos = models.JSONField(_('photos du bateau'), default=list)
    rate_per_km = models.DecimalField(_('tarif par kilomètre (€)'), max_digits=7, decimal_places=2, null=True, blank=True)
    rate_per_hour = models.DecimalField(_('tarif par heure (€)'), max_digits=7, decimal_places=2, null=True, blank=True)

    def __str__(self):
        return f'Capitaine: {self.user.email}'

class Establishment(models.Model):
    class Types(models.TextChoices):
        RESTAURANT = 'RESTAURANT', _('Restaurant')
        HOTEL = 'HOTEL', _('Hôtel')
        PRIVATE_BEACH = 'PRIVATE_BEACH', _('Plage privée')

    user = models.OneToOneField(User, on_delete=models.CASCADE, primary_key=True)
    name = models.CharField(_('nom'), max_length=100)
    type = models.CharField(_('type d\'établissement'), max_length=20, choices=Types.choices)
    address = models.CharField(_('adresse'), max_length=255)
    description = models.TextField(_('description'))
    main_photo = models.ImageField(_('photo principale'), upload_to='establishment_photos/')
    secondary_photos = models.JSONField(_('photos secondaires'), default=list)
    wallet_balance = models.DecimalField(_('solde du portefeuille'), max_digits=10, decimal_places=2, default=0)
    business_name = models.CharField(_('nom commercial'), max_length=100, blank=True)
    business_type = models.CharField(_('type d\'activité'), max_length=50, blank=True)
    registration_number = models.CharField(_('numéro d\'enregistrement'), max_length=50, blank=True)
    tax_id = models.CharField(_('numéro de TVA'), max_length=50, blank=True)
    opening_hours = models.JSONField(_('horaires d\'ouverture'), default=dict)
    services_offered = models.JSONField(_('services proposés'), default=list)
    average_rating = models.DecimalField(_('note moyenne'), max_digits=3, decimal_places=2, default=0)
    location_coordinates = models.CharField(_('coordonnées GPS'), max_length=50, blank=True)
    website = models.URLField(_('site web'), blank=True)
    social_media = models.JSONField(_('réseaux sociaux'), default=dict)

    def __str__(self):
        return f'{self.type}: {self.name}'
