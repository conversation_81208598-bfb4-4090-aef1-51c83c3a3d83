from rest_framework import serializers
from django.contrib.contenttypes.models import ContentType
from .models import Review, ReviewResponse, ReviewReport
from accounts.serializers import UserSerializer
from trips.serializers import TripSerializer

class ReviewResponseSerializer(serializers.ModelSerializer):
    author = UserSerializer(read_only=True)

    class Meta:
        model = ReviewResponse
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')

class ReviewSerializer(serializers.ModelSerializer):
    author = UserSerializer(read_only=True)
    trip = TripSerializer(read_only=True)
    responses = ReviewResponseSerializer(many=True, read_only=True)
    average_rating = serializers.SerializerMethodField()
    content_type = serializers.PrimaryKeyRelatedField(
        queryset=ContentType.objects.all(),
        write_only=True
    )
    object_id = serializers.IntegerField(write_only=True)
    reviewed_object_info = serializers.SerializerMethodField()

    class Meta:
        model = Review
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'is_verified',
                          'reported_count')

    def get_average_rating(self, obj):
        return obj.calculate_average_rating()

    def get_reviewed_object_info(self, obj):
        if obj.reviewed_object:
            return {
                'type': obj.reviewed_object.__class__.__name__,
                'id': obj.reviewed_object.id,
                'name': str(obj.reviewed_object)
            }
        return None

    def validate(self, data):
        # Vérifier que l'objet évalué existe
        try:
            content_type = data.get('content_type')
            object_id = data.get('object_id')
            if content_type and object_id:
                content_type.get_object_for_this_type(id=object_id)
        except:
            raise serializers.ValidationError(
                "L'objet évalué n'existe pas"
            )

        return data

class ReviewListSerializer(serializers.ModelSerializer):
    """Sérialiseur simplifié pour la liste des avis"""
    author_name = serializers.CharField(source='author.get_full_name', read_only=True)
    response_count = serializers.SerializerMethodField()

    class Meta:
        model = Review
        fields = ('id', 'author_name', 'rating', 'title', 'created_at',
                 'is_verified', 'response_count')

    def get_response_count(self, obj):
        return obj.responses.count()

class ReviewReportSerializer(serializers.ModelSerializer):
    reporter = UserSerializer(read_only=True)
    review = ReviewSerializer(read_only=True)

    class Meta:
        model = ReviewReport
        fields = '__all__'
        read_only_fields = ('created_at', 'status', 'resolved_at',
                          'resolution_notes')

    def validate(self, data):
        # Vérifier qu'un utilisateur ne signale pas plusieurs fois le même avis
        review = data.get('review')
        reporter = self.context.get('request').user
        if ReviewReport.objects.filter(review=review, reporter=reporter).exists():
            raise serializers.ValidationError(
                "Vous avez déjà signalé cet avis"
            )
        return data

class ReviewStatisticsSerializer(serializers.Serializer):
    """Sérialiseur pour les statistiques d'avis"""
    total_reviews = serializers.IntegerField()
    average_rating = serializers.FloatField()
    rating_distribution = serializers.DictField()
    recent_reviews = ReviewListSerializer(many=True)
    total_verified_reviews = serializers.IntegerField()
    response_rate = serializers.FloatField()

class ReviewFilterSerializer(serializers.Serializer):
    """Sérialiseur pour les filtres d'avis"""
    rating = serializers.IntegerField(required=False, min_value=1, max_value=5)
    verified_only = serializers.BooleanField(required=False)
    has_response = serializers.BooleanField(required=False)
    date_from = serializers.DateField(required=False)
    date_to = serializers.DateField(required=False)
    sort_by = serializers.ChoiceField(
        choices=['rating', '-rating', 'created_at', '-created_at'],
        required=False
    )
