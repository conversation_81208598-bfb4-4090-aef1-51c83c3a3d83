from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import Trip, Shuttle, Location

@admin.register(Trip)
class TripAdmin(admin.ModelAdmin):
    list_display = ('client', 'captain', 'boat', 'start_location', 'end_location', 
                   'scheduled_start_time', 'status', 'total_price', 'payment_status')
    list_filter = ('status', 'payment_status', 'captain', 'boat', 'establishment')
    search_fields = ('client__user__email', 'captain__user__email', 'boat__name',
                    'start_location', 'end_location')
    readonly_fields = ('created_at', 'updated_at', 'total_price')
    date_hierarchy = 'scheduled_start_time'
    
    fieldsets = (
        (_('Participants'), {
            'fields': ('client', 'captain', 'boat', 'establishment')
        }),
        (_('Trajet'), {
            'fields': ('start_location', 'end_location', 'scheduled_start_time',
                      'scheduled_end_time', 'actual_start_time', 'actual_end_time')
        }),
        (_('Passagers'), {
            'fields': ('passenger_count', 'passenger_names', 'special_requests')
        }),
        (_('Suivi'), {
            'fields': ('status', 'current_location', 'tracking_data')
        }),
        (_('Paiement'), {
            'fields': ('base_price', 'additional_charges', 'tip', 'total_price',
                      'payment_status', 'payment_method')
        }),
        (_('Horodatage'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(Shuttle)
class ShuttleAdmin(admin.ModelAdmin):
    list_display = ('route_name', 'establishment', 'captain', 'boat', 'departure_time',
                   'status', 'current_bookings', 'max_capacity', 'is_recurring')
    list_filter = ('status', 'is_recurring', 'establishment', 'captain', 'boat')
    search_fields = ('route_name', 'start_location', 'end_location', 'captain__user__email')
    readonly_fields = ('created_at', 'updated_at')
    date_hierarchy = 'departure_time'
    
    fieldsets = (
        (_('Informations générales'), {
            'fields': ('route_name', 'establishment', 'captain', 'boat')
        }),
        (_('Trajet'), {
            'fields': ('start_location', 'end_location', 'stops')
        }),
        (_('Horaires'), {
            'fields': ('departure_time', 'arrival_time', 'frequency', 'days_of_week',
                      'is_recurring')
        }),
        (_('Capacité et tarification'), {
            'fields': ('max_capacity', 'current_bookings', 'price_per_person')
        }),
        (_('Statut et politique'), {
            'fields': ('status', 'cancellation_policy')
        }),
        (_('Horodatage'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(Location)
class LocationAdmin(admin.ModelAdmin):
    list_display = ('trip', 'latitude', 'longitude', 'speed', 'heading', 'timestamp')
    list_filter = ('trip', 'timestamp')
    search_fields = ('trip__client__user__email', 'trip__captain__user__email')
    readonly_fields = ('timestamp',)
    date_hierarchy = 'timestamp'
    
    fieldsets = (
        (_('Trip'), {
            'fields': ('trip',)
        }),
        (_('Coordonnées'), {
            'fields': ('latitude', 'longitude', 'accuracy', 'altitude')
        }),
        (_('Navigation'), {
            'fields': ('speed', 'heading')
        }),
        (_('Horodatage'), {
            'fields': ('timestamp',)
        })
    )
