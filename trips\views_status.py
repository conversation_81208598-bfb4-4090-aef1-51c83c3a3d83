"""
Vues pour la gestion des statuts des courses
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.utils import timezone
from .models import Trip
from .serializers import TripSerializer


class TripStartView(APIView):
    """Démarrer une course (ACCEPTED → IN_PROGRESS)"""
    permission_classes = [IsAuthenticated]

    def patch(self, request, trip_id):
        trip = get_object_or_404(Trip, id=trip_id)
        
        # Vérifier que l'utilisateur est le capitaine de cette course
        if not hasattr(request.user, 'captain') or trip.captain != request.user.captain:
            return Response(
                {'error': 'Seul le capitaine assigné peut démarrer cette course'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Vérifier que la course peut être démarrée
        if not trip.can_start():
            return Response(
                {'error': f'Cette course ne peut pas être démarrée. Statut actuel: {trip.status}'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Démarrer la course
        if trip.start_trip(request.user):
            return Response({
                'message': 'Course démarrée avec succès',
                'trip': TripSerializer(trip).data
            })
        else:
            return Response(
                {'error': 'Impossible de démarrer la course'},
                status=status.HTTP_400_BAD_REQUEST
            )


class TripCompleteView(APIView):
    """Terminer une course (IN_PROGRESS → COMPLETED)"""
    permission_classes = [IsAuthenticated]

    def patch(self, request, trip_id):
        trip = get_object_or_404(Trip, id=trip_id)
        
        # Vérifier que l'utilisateur est le capitaine de cette course
        if not hasattr(request.user, 'captain') or trip.captain != request.user.captain:
            return Response(
                {'error': 'Seul le capitaine assigné peut terminer cette course'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Vérifier que la course peut être terminée
        if not trip.can_complete():
            return Response(
                {'error': f'Cette course ne peut pas être terminée. Statut actuel: {trip.status}'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Récupérer les notes optionnelles
        captain_notes = request.data.get('captain_notes', '')
        if captain_notes:
            trip.captain_notes += f"\n{captain_notes}"
        
        # Terminer la course
        if trip.complete_trip(request.user):
            return Response({
                'message': 'Course terminée avec succès',
                'trip': TripSerializer(trip).data,
                'duration_minutes': trip.calculate_duration()
            })
        else:
            return Response(
                {'error': 'Impossible de terminer la course'},
                status=status.HTTP_400_BAD_REQUEST
            )


class TripCancelView(APIView):
    """Annuler une course"""
    permission_classes = [IsAuthenticated]

    def patch(self, request, trip_id):
        trip = get_object_or_404(Trip, id=trip_id)
        
        # Vérifier les permissions
        is_client = hasattr(request.user, 'client') and trip.client == request.user.client
        is_captain = hasattr(request.user, 'captain') and trip.captain == request.user.captain
        
        if not (is_client or is_captain):
            return Response(
                {'error': 'Seuls le client ou le capitaine peuvent annuler cette course'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Vérifier que la course peut être annulée
        if not trip.can_cancel():
            return Response(
                {'error': f'Cette course ne peut pas être annulée. Statut actuel: {trip.status}'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Récupérer la raison d'annulation
        cancellation_reason = request.data.get('reason', '')
        if not cancellation_reason:
            return Response(
                {'error': 'Une raison d\'annulation est requise'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Annuler la course
        if trip.cancel_trip(request.user, cancellation_reason):
            user_type = "client" if is_client else "capitaine"
            return Response({
                'message': f'Course annulée par le {user_type}',
                'trip': TripSerializer(trip).data
            })
        else:
            return Response(
                {'error': 'Impossible d\'annuler la course'},
                status=status.HTTP_400_BAD_REQUEST
            )


class TripStatusView(APIView):
    """Obtenir le statut détaillé d'une course"""
    permission_classes = [IsAuthenticated]

    def get(self, request, trip_id):
        trip = get_object_or_404(Trip, id=trip_id)
        
        # Vérifier les permissions
        is_client = hasattr(request.user, 'client') and trip.client == request.user.client
        is_captain = hasattr(request.user, 'captain') and trip.captain == request.user.captain
        
        if not (is_client or is_captain):
            return Response(
                {'error': 'Accès refusé'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Calculer des informations supplémentaires
        duration = trip.calculate_duration()
        
        response_data = {
            'trip': TripSerializer(trip).data,
            'status_info': {
                'can_start': trip.can_start(),
                'can_complete': trip.can_complete(),
                'can_cancel': trip.can_cancel(),
                'duration_minutes': duration,
                'is_delayed': trip.delay_minutes > 0,
                'has_problems': bool(trip.problem_description)
            }
        }
        
        return Response(response_data)


class TripProblemView(APIView):
    """Signaler un problème sur une course"""
    permission_classes = [IsAuthenticated]

    def patch(self, request, trip_id):
        trip = get_object_or_404(Trip, id=trip_id)
        
        # Vérifier que l'utilisateur est le capitaine de cette course
        if not hasattr(request.user, 'captain') or trip.captain != request.user.captain:
            return Response(
                {'error': 'Seul le capitaine peut signaler un problème'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        problem_description = request.data.get('problem_description', '')
        delay_minutes = request.data.get('delay_minutes', 0)
        
        if not problem_description:
            return Response(
                {'error': 'Une description du problème est requise'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Mettre à jour le statut et les informations
        trip.status = Trip.Status.PROBLEM
        trip.problem_description = problem_description
        trip.delay_minutes = int(delay_minutes)
        trip.save()
        
        return Response({
            'message': 'Problème signalé avec succès',
            'trip': TripSerializer(trip).data
        })


class TripDelayView(APIView):
    """Signaler un retard sur une course"""
    permission_classes = [IsAuthenticated]

    def patch(self, request, trip_id):
        trip = get_object_or_404(Trip, id=trip_id)
        
        # Vérifier que l'utilisateur est le capitaine de cette course
        if not hasattr(request.user, 'captain') or trip.captain != request.user.captain:
            return Response(
                {'error': 'Seul le capitaine peut signaler un retard'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        delay_minutes = request.data.get('delay_minutes', 0)
        reason = request.data.get('reason', '')
        
        if delay_minutes <= 0:
            return Response(
                {'error': 'Le retard doit être supérieur à 0 minutes'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Mettre à jour le statut et les informations
        trip.status = Trip.Status.DELAYED
        trip.delay_minutes = int(delay_minutes)
        if reason:
            trip.captain_notes += f"\nRetard de {delay_minutes} minutes: {reason}"
        
        # Recalculer l'heure d'arrivée estimée
        if trip.estimated_arrival_time:
            trip.estimated_arrival_time += timezone.timedelta(minutes=delay_minutes)
        
        trip.save()
        
        return Response({
            'message': f'Retard de {delay_minutes} minutes signalé',
            'trip': TripSerializer(trip).data
        })
